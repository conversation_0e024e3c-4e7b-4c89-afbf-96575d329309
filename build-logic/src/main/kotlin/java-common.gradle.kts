import org.gradle.api.artifacts.VersionCatalogsExtension
import org.gradle.kotlin.dsl.getByType
import java.util.concurrent.TimeUnit

plugins {
    `java-library`
    id("com.diffplug.spotless")
}

val libs = extensions.getByType<VersionCatalogsExtension>().named("libs")

dependencies {
    // 导入 BOM 平台，统一版本管理
    implementation(platform(libs.findLibrary("spring-boot-bom").get()))
    implementation(platform(libs.findLibrary("spring-framework-bom").get()))
    implementation(platform(libs.findLibrary("spring-ai-bom").get()))

    compileOnly(libs.findLibrary("lombok").get())
    annotationProcessor(libs.findLibrary("lombok").get())
    testImplementation(libs.findLibrary("spring-boot-starter-testing").get())
    testCompileOnly(libs.findLibrary("lombok").get())
    testAnnotationProcessor(libs.findLibrary("lombok").get())
    testRuntimeOnly(libs.findLibrary("h2").get())
}

// Java 编译配置
java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
    withSourcesJar()

    // 改善 IDEA 集成
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

tasks.withType<JavaCompile> {
    options.isIncremental = true
    options.encoding = "UTF-8"
    options.compilerArgs.addAll(listOf(
        "-parameters",
        "-Xlint:unchecked",
        "-Xlint:deprecation"
    ))
}

// 测试配置
tasks.withType<Test> {
    useJUnitPlatform()
    testLogging {
        events("passed", "skipped", "failed")
        showStandardStreams = false
    }
    // 测试并行执行
    maxParallelForks = (Runtime.getRuntime().availableProcessors() / 2).takeIf { it > 0 } ?: 1
}

// 代码格式化 - 只保留import限制
spotless {
    java {
        removeUnusedImports()
        // 移除其他格式化规则，只保留import管理
    }
}

// IDEA 兼容性配置
configurations.all {
    // 确保依赖解析的一致性
    resolutionStrategy {
        // 缓存动态版本1小时
        cacheDynamicVersionsFor(1, TimeUnit.HOURS)
        // 缓存变化模块1小时
        cacheChangingModulesFor(1, TimeUnit.HOURS)

        // 强制使用一致的版本
        force(
            "org.slf4j:slf4j-api:2.0.17",
            "com.fasterxml.jackson.core:jackson-core:2.18.3",
            "com.fasterxml.jackson.core:jackson-databind:2.18.3",
            "com.fasterxml.jackson.core:jackson-annotations:2.18.3"
        )
    }
}