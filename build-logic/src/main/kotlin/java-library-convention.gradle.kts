import org.gradle.api.artifacts.VersionCatalogsExtension
import org.gradle.kotlin.dsl.getByType

plugins {
    id("java-common")
}

val libs = extensions.getByType<VersionCatalogsExtension>().named("libs")

dependencies {
    // Guava 依赖移到 ax-framework 模块，因为不是所有模块都需要
    // 其他公共依赖已在 java-common 中提供，此处无需重复
}

// spring-boot-starter-logging 排除已移到根项目统一管理

tasks.withType<JavaCompile>().configureEach {
    options.compilerArgs.add("-parameters")
}