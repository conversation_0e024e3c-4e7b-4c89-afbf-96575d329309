plugins {
    `java-library`
}

description = "AX-ADMIN 框架核心模块"

dependencies {
    api(libs.spring.boot.starter.web)
    api(libs.spring.boot.starter.validation)
    api(libs.spring.boot.starter.aop)
    api(libs.spring.boot.starter.cache)
    api(libs.spring.boot.starter.mail)
    api(libs.spring.boot.starter.actuator)
    api(libs.spring.boot.starter.data.redis)
    api(libs.mybatis.plus.spring.boot3.starter)
    api(libs.mybatis.plus.jsqlparser)
    api(libs.mybatis.plus.extension)
    api(libs.hutool)
    api(libs.guava)
    api(libs.log4j.slf4j2.impl)
    api(libs.log4j.core)
    api(libs.log4j.api)
    api(libs.redisson.spring.boot.starter)
    api(libs.caffeine)
    api(libs.fastjson)
    api(libs.poi)
    api(libs.poi.ooxml)
    api(libs.poi.scratchpad)
    api(libs.ooxml.schemas)
    api(libs.velocity.engine.core)
    api(libs.velocity.tools.generic)
    api(libs.freemarker)
    api(libs.knife4j.openapi3.jakarta.spring.boot.starter)
    api(libs.commons.lang3)
    api(libs.commons.text)
    api(libs.commons.collections4)
    api(libs.commons.compress)
    api(libs.commons.io)
    api(libs.commons.codec)
    api(libs.postgresql)
    api(libs.druid.spring.boot3.starter)
    api(libs.fast.excel)
    api(libs.ip2region)
    api(libs.sa.token.spring.boot3.starter)
    api(libs.aliyun.oss)
    api(libs.jsoup)
    api(libs.concurrent.linked.hashmap)
    api(libs.spring.security.crypto)
    api(libs.hibernate.validator)
    api(libs.reflections)
    api(libs.tika.core)
    api(libs.bouncycastle.provider)
} 