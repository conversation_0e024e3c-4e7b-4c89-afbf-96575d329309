package com.anxys.framework.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 异步缓存初始化配置
 * 开发环境专用，用于提升启动速度
 *
 * <AUTHOR>
 */
@Configuration
@Profile("dev")
public class AsyncCacheConfig {

    /**
     * 缓存初始化专用线程池
     * 用于异步初始化各种缓存，避免阻塞主启动流程
     */
    @Bean("cacheInitExecutor")
    public TaskExecutor cacheInitExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);              // 核心线程数
        executor.setMaxPoolSize(4);               // 最大线程数
        executor.setQueueCapacity(10);            // 队列容量
        executor.setThreadNamePrefix("cache-init-"); // 线程名前缀
        executor.setWaitForTasksToCompleteOnShutdown(true); // 关闭时等待任务完成
        executor.setAwaitTerminationSeconds(10);  // 等待终止时间
        executor.initialize();
        return executor;
    }
}
