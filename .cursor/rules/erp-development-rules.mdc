---
description: 
globs: 
alwaysApply: true
---
---
description: ERP系统前后端全栈开发规范与最佳实践
globs: 
alwaysApply: true
---

# ERP系统开发规范 📚

## Context
- 适用于ax-admin系统的开发规范
- 确保代码质量和团队协作效率
- 统一开发标准和最佳实践

## 目录结构规范 📁

### 1. 后端领域模型
```mermaid
graph TD
    A[module.business.实体名]
    A -->|数据模型| B[domain]
    B --> C[实体名.java]
    B --> D[form]
    B --> E[vo]
    B --> F[excel]
    D -->|新增| G[实体名AddForm.java]
    D -->|更新| H[实体名UpdateForm.java]
    D -->|查询| I[实体名QueryForm.java]
    D -->|分页| J[实体名PageForm.java]
    E -->|视图| K[实体名VO.java]
    F -->|导入| L[实体名ExcelImport.java]
    F -->|导出| M[实体名ExcelExport.java]
```

### 2. 前端结构
```mermaid
graph TD
    A[views.business.实体名]
    A --> B[api]
    A --> C[components]
    A --> D[model]
    B --> E[实体名-api.ts]
    C --> F[实体名Form.vue]
    C --> G[实体名List.vue]
    D --> H[实体名-model.ts]
```

## Critical Rules 💡

### 1. 数据规范 (PostgreSQL)
- **字段类型**
  ```sql
  -- 状态标识: boolean
  is_deleted BOOLEAN DEFAULT FALSE
  
  -- 字典项: varchar(20)
  status VARCHAR(20)
  
  -- 普通文本: varchar(50/100/255)
  name VARCHAR(50)
  
  -- 字段注释
  COMMENT ON COLUMN erp_user.status IS '状态:[enable-启用,disable-禁用]';
  ```

### 2. 命名规范
- **Java类命名**
  ```java
  public class UserAddForm {}     // 新增表单
  public class UserUpdateForm {}  // 更新表单
  public class UserQueryForm {}   // 查询表单
  public class UserVO {}         // 视图对象
  ```
- **数据库命名**
  ```sql
  -- 表名: 模块_实体
  CREATE TABLE erp_user (
    -- 字段: 下划线分隔
    user_name VARCHAR(50)
  );
  
  -- 表注释
  COMMENT ON TABLE erp_user IS '用户信息表';
  -- 字段注释
  COMMENT ON COLUMN erp_user.user_name IS '用户名称';
  ```

### 3. 开发规范
- **SQL优化**
  ```sql
  -- 正确示例：批量查询
  SELECT t.* FROM erp_user t WHERE t.id = ANY(#{ids})
  
  -- 正确示例：JSON查询
  SELECT * FROM erp_user WHERE data_json->>'key' = 'value'
  
  -- 错误示例：循环查询
  SELECT * FROM erp_user WHERE id = #{id}
  ```

- **状态处理**
  ```typescript
  // 前端处理状态显示
  const statusMap = {
    enable: '启用',
    disable: '禁用'
  }
  ```

### 4. 接口规范
- **API路径**: `/模块/实体/操作`
- **返回格式**:
  ```json
  {
    "code": 200,
    "msg": "success",
    "data": {}
  }
  ```

## 最佳实践 ⭐

### 1. 查询优化 (PostgreSQL)
```java
// 推荐：批量查询
@Select("SELECT * FROM erp_user WHERE id = ANY(#{ids})")
List<User> batchQuery(List<Long> ids);

// 推荐：使用JSONB类型
@Select("SELECT * FROM erp_box WHERE box_config->>'type' = #{type}")
List<Box> queryByConfigType(String type);

// 推荐：Map分组
Map<Long, User> userMap = userList.stream()
    .collect(Collectors.toMap(User::getId, u -> u));
```

### 2. 前端组件
```vue
<!-- 推荐：使用统一的操作按钮 -->
<AxTableOperateButtons
  :operate-param="{
    add: { privilege: 'user:add' },
    edit: { privilege: 'user:edit' }
  }"
/>
```

## 注意事项 ⚠️
1. 所有新功能必须遵循目录结构规范
2. 禁止在Service层进行状态值转换
3. 新增字段必须指定明确的数据类型和长度
4. 查询必须考虑性能优化
5. 前端展示逻辑与后端数据处理严格分离
6. 复杂数据结构优先使用JSONB类型
7. 使用PostgreSQL的事务和约束功能保证数据一致性

## 代码审查清单 ✅
1. [ ] 目录结构是否符合规范
2. [ ] 命名是否符合规范
3. [ ] 是否包含必要的注释
4. [ ] 是否进行了性能优化
5. [ ] 是否遵循前后端分离原则











