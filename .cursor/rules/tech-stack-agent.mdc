---
description: 
globs: 
alwaysApply: false
---
---
description: ax-admin项目技术栈概览和架构说明
globs: 
alwaysApply: false
---

# ax-admin 项目技术栈说明 🏗️

## Context
- 帮助新团队成员快速了解项目架构
- 提供技术选型和版本信息
- 明确前后端技术栈和开发规范

## 项目架构概览

```mermaid
graph TD
    A[ax-admin系统] --> B[前端: Vue3 + Vite]
    A --> C[后端: Spring Boot 3]
    B --> D[UI: Ant Design Vue 4.x + PrimeVue 4.x]
    B --> E[状态管理: Pinia]
    B --> F[路由: Vue Router 4]
    C --> G[数据库: PostgreSQL]
    C --> H[ORM: MyBatis-Plus 3.5.x]
    C --> I[API文档: knife4j 4.x]
```

## Critical Rules

### 1. 前端技术栈

- **核心框架**: 
  ```
  Vue 3.5.x (Composition API)
  TypeScript 5.6.x
  Vite 6.x (构建工具)
  ```

- **UI组件库**: 
  ```
  Ant Design Vue 4.2.x - 主要组件库
  PrimeVue 4.3.x - 补充组件库
  ```

- **状态管理**: 
  ```
  Pinia 2.3.x
  ```

- **路由管理**: 
  ```
  Vue Router 4.5.x
  ```

- **工具库**: 
  ```
  lodash-es - 实用工具函数
  dayjs - 日期处理
  axios - HTTP请求
  echarts - 图表展示
  ```

### 2. 后端技术栈

- **核心框架**: 
  ```
  Spring Boot 3.3.x
  Java 17
  ```

- **ORM层**: 
  ```
  MyBatis-Plus 3.5.x
  ```

- **数据库**: 
  ```
  PostgreSQL 14+
  ```

- **API文档**: 
  ```
  knife4j 4.4.x (基于Swagger3)
  ```

- **安全框架**: 
  ```
  Spring Security Crypto
  ```

- **工具库**: 
  ```
  Hutool 5.8.x - 工具集
  Fastjson 2.0.x - JSON处理
  ```

## 项目模块说明

### 1. 前端结构
admin-web/
├── src/
│ ├── api/ - API接口定义
│ ├── components/ - 公共组件
│ ├── views/ - 页面视图
│ ├── router/ - 路由配置
│ ├── store/ - Pinia状态管理
│ ├── utils/ - 工具函数
│ └── assets/ - 静态资源

### 2. 后端结构

ax-admin/ - 后台管理模块
├── src/main/java/net/lab1024/sa/admin/
│ ├── common/ - 公共工具
│ ├── module/ - 业务模块
│ ├── config/ - 配置类
│ └── util/ - 工具类
sa-base/ - 基础核心模块
├── src/main/java/net/lab1024/sa/base/
│ ├── common/ - 公共基础
│ ├── module/ - 基础模块
│ └── config/ - 基础配置


## 开发流程和环境

### 开发环境
- Node.js 20+
- JDK 17+
- PostgreSQL 14+
- PNPM 9+

### 本地开发命令
```bash
# 前端开发
cd admin-web
pnpm install
pnpm dev

# 后端开发
mvn clean install
```

## 注意事项 ⚠️
1. 前端使用Composition API开发，不要使用Options API
2. PostgreSQL数据库中复杂数据结构使用JSONB类型
3. 开发时遵循既定的目录结构和命名规范
4. 使用PrimeVue和Ant Design Vue时注意组件风格一致性
5. 遵循领域驱动设计原则组织后端代码

## 版本管理 🔄
1. 使用Git Flow工作流
2. 主分支: `main`/`master`
3. 开发分支: `develop`
4. 功能分支: `feature/xxx`
5. 发布分支: `release/x.x.x`


