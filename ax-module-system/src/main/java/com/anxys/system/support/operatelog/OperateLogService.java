package com.anxys.system.support.operatelog;

import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import com.anxys.system.support.operatelog.domain.OperateLogEntity;
import com.anxys.system.support.operatelog.domain.OperateLogQueryForm;
import com.anxys.system.support.operatelog.domain.OperateLogVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/** 操作日志 */
@Service
public class OperateLogService {

  @Resource private OperateLogDao operateLogDao;

  /**
   * 分页查询
   *
   * @param queryForm 查询条件
   * @return 分页结果
   */
  public HttpResponse<PageResult<OperateLogVO>> queryByPage(OperateLogQueryForm queryForm) {
    Page<OperateLogEntity> page = PageUtil.convert2PageQuery(queryForm);
    List<OperateLogEntity> logEntityList = operateLogDao.queryByPage(page, queryForm);
    PageResult<OperateLogVO> pageResult =
        PageUtil.convert2PageResult(page, logEntityList, OperateLogVO.class);
    return HttpResponse.ok(pageResult);
  }

  /**
   * 查询详情
   *
   * @param operateLogId 操作日志ID
   * @return 操作日志详情
   */
  public HttpResponse<OperateLogVO> detail(Long operateLogId) {
    OperateLogEntity operateLogEntity = operateLogDao.selectById(operateLogId);
    if (operateLogEntity == null) {
      return HttpResponse.error(UserErrorCode.DATA_NOT_EXIST);
    }
    OperateLogVO operateLogVO = BeanUtil.copy(operateLogEntity, OperateLogVO.class);
    return HttpResponse.ok(operateLogVO);
  }
}
