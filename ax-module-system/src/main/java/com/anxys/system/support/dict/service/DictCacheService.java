package com.anxys.system.support.dict.service;

import cn.hutool.core.util.StrUtil;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.system.support.dict.dao.DictKeyDao;
import com.anxys.system.support.dict.dao.DictValueDao;
import com.anxys.system.support.dict.domain.entity.DictKeyEntity;
import com.anxys.system.support.dict.domain.entity.DictValueEntity;
import com.anxys.system.support.dict.domain.vo.DictValueVO;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/** 字典缓存 服务 */
@Slf4j
@Service
public class DictCacheService {

  @Resource private DictKeyDao dictKeyDao;
  @Resource private DictValueDao dictValueDao;

  private final ConcurrentHashMap<String, List<DictValueVO>> DICT_CACHE = new ConcurrentHashMap<>();

  /** 缓存初始化状态标记 */
  private final AtomicBoolean cacheInitialized = new AtomicBoolean(false);

  /** 异步缓存初始化线程池（开发环境专用） */
  @Resource
  @Qualifier("cacheInitExecutor")
  private TaskExecutor cacheInitExecutor;

  /**
   * 应用启动完成后异步初始化字典缓存
   * 开发环境使用异步初始化，生产环境使用同步初始化
   */
  @EventListener(ApplicationReadyEvent.class)
  public void onApplicationReady() {
    if (cacheInitExecutor != null) {
      // 开发环境：异步初始化缓存
      log.info("🚀 开发环境检测到，启用字典缓存异步初始化...");
      cacheInitExecutor.execute(() -> {
        try {
          // 延迟1.5秒，确保应用完全启动且避免与配置缓存冲突
          Thread.sleep(1500);
          this.cacheInit();
        } catch (InterruptedException e) {
          Thread.currentThread().interrupt();
          log.warn("字典缓存初始化被中断", e);
        }
      });
    } else {
      // 生产环境：同步初始化缓存
      log.info("📋 生产环境检测到，启用字典缓存同步初始化...");
      this.cacheInit();
    }
  }

  public void cacheInit() {
    long startTime = System.currentTimeMillis();
    DICT_CACHE.clear();
    List<DictKeyEntity> dictKeyEntityList = dictKeyDao.selectByDeletedFlag(false);
    if (CollectionUtils.isEmpty(dictKeyEntityList)) {
      cacheInitialized.set(true);
      return;
    }
    List<DictValueEntity> dictKeyValueList = dictValueDao.selectByDeletedFlag(false);
    List<DictValueVO> dictValueVOList = BeanUtil.copyList(dictKeyValueList, DictValueVO.class);
    Map<Long, List<DictValueVO>> valueListMap =
        dictValueVOList.stream().collect(Collectors.groupingBy(DictValueVO::getDictKeyId));
    // 字典键值对缓存
    for (DictKeyEntity dictKeyEntity : dictKeyEntityList) {
      String keyCode = dictKeyEntity.getKeyCode();
      Long dictKeyId = dictKeyEntity.getDictKeyId();
      DICT_CACHE.put(keyCode, valueListMap.getOrDefault(dictKeyId, Lists.newArrayList()));
    }

    cacheInitialized.set(true);
    long duration = System.currentTimeMillis() - startTime;
    log.info("################# 数据字典缓存初始化完毕 (耗时:{}ms) ###################", duration);
  }

  /** 刷新缓存 */
  public HttpResponse<String> cacheRefresh() {
    DICT_CACHE.clear();
    this.cacheInit();
    return HttpResponse.ok();
  }

  /**
   * 查询某个key对应的字典值列表 - 支持懒加载
   *
   * @param keyCode 字典键编码
   * @return 字典值列表
   */
  public List<DictValueVO> selectByKeyCode(String keyCode) {
    // 如果缓存未初始化，则同步加载（兜底机制）
    if (!cacheInitialized.get() && DICT_CACHE.isEmpty()) {
      synchronized (this) {
        if (!cacheInitialized.get() && DICT_CACHE.isEmpty()) {
          log.info("字典缓存未初始化，执行同步加载...");
          this.cacheInit();
        }
      }
    }

    return DICT_CACHE.getOrDefault(keyCode, Lists.newArrayList());
  }

  /**
   * 查询字典值名称
   *
   * @param keyCode 字典键编码
   * @param valueCode 字典值编码
   * @return 值名称
   */
  public String selectValueNameByValueCode(String keyCode, String valueCode) {
    DictValueVO dictValueVO = this.selectValueByValueCode(keyCode, valueCode);
    if (dictValueVO == null) {
      return "";
    }
    return dictValueVO.getValueName();
  }

  /**
   * @param keyCode 字典键编码
   * @param valueCode 字典值编码
   * @return 字典值
   */
  public DictValueVO selectValueByValueCode(String keyCode, String valueCode) {
    if (StrUtil.isEmpty(valueCode)) {
      return null;
    }
    if (StrUtil.isEmpty(keyCode)) {
      return null;
    }

    List<DictValueVO> dictValueVOList = selectByKeyCode(keyCode); // 使用支持懒加载的方法
    if (CollectionUtils.isEmpty(dictValueVOList)) {
      return null;
    }
    Optional<DictValueVO> option =
        dictValueVOList.stream().filter(e -> e.getValueCode().equals(valueCode)).findFirst();
    return option.orElse(null);
  }

  public String selectValueNameByValueCodeSplit(String keyCode, String valueCodes) {
    if (StrUtil.isEmpty(valueCodes)) {
      return "";
    }
    List<String> valueNameList = Lists.newArrayList();
    String[] valueCodeArray = valueCodes.split(",");
    for (String valueCode : valueCodeArray) {
      DictValueVO dictValueVO = this.selectValueByValueCode(keyCode, valueCode);
      if (dictValueVO != null) {
        valueNameList.add(dictValueVO.getValueName());
      }
    }
    return StringUtils.join(valueNameList, ",");
  }
}
