package com.anxys.system.support.config;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import com.anxys.framework.constant.ReloadConst;
import com.anxys.system.support.config.domain.*;
import com.anxys.system.support.reload.core.annoation.Reload;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/** 系统配置业务类 */
@Slf4j
@Service
public class ConfigService {

  /** 一个简单的系统配置缓存 */
  private final ConcurrentHashMap<String, ConfigEntity> CONFIG_CACHE = new ConcurrentHashMap<>();

  /** 缓存初始化状态标记 */
  private final AtomicBoolean cacheInitialized = new AtomicBoolean(false);

  @Resource private ConfigDao configDao;

  /** 异步缓存初始化线程池（开发环境专用） */
  @Resource
  @Qualifier("cacheInitExecutor")
  private TaskExecutor cacheInitExecutor;

  @Reload(ReloadConst.CONFIG_RELOAD)
  public void configReload(String param) {
    this.loadConfigCache();
  }

  /**
   * 应用启动完成后异步初始化缓存
   * 开发环境使用异步初始化，生产环境使用同步初始化
   */
  @EventListener(ApplicationReadyEvent.class)
  public void onApplicationReady() {
    if (cacheInitExecutor != null) {
      // 开发环境：异步初始化缓存
      log.info("🚀 开发环境检测到，启用异步缓存初始化...");
      cacheInitExecutor.execute(() -> {
        try {
          // 延迟1秒，确保应用完全启动
          Thread.sleep(1000);
          Thread.sleep(1000);
          this.loadConfigCache();
        } catch (InterruptedException e) {
          Thread.currentThread().interrupt();
          log.warn("系统配置缓存初始化被中断", e);
        }
      });
    } else {
      // 生产环境：同步初始化缓存
      log.info("📋 生产环境检测到，启用同步缓存初始化...");
      this.loadConfigCache();
    }
  }

  /** 初始化系统设置缓存 */
  private void loadConfigCache() {
    long startTime = System.currentTimeMillis();
    CONFIG_CACHE.clear();
    List<ConfigEntity> entityList = configDao.selectList(null);
    if (CollectionUtils.isEmpty(entityList)) {
      cacheInitialized.set(true);
      return;
    }
    entityList.forEach(
        entity -> this.CONFIG_CACHE.put(entity.getConfigKey().toLowerCase(), entity));

    cacheInitialized.set(true);
    long duration = System.currentTimeMillis() - startTime;
    log.info("################# 系统配置缓存初始化完毕:{} (耗时:{}ms) ###################",
        CONFIG_CACHE.size(), duration);
  }

  /** 刷新系统设置缓存 */
  private void refreshConfigCache(Long configId) {
    // 重新查询 加入缓存
    ConfigEntity configEntity = configDao.selectById(configId);
    if (null == configEntity) {
      return;
    }
    this.CONFIG_CACHE.put(configEntity.getConfigKey().toLowerCase(), configEntity);
  }

  /** 分页查询系统配置 */
  public HttpResponse<PageResult<ConfigVO>> queryConfigPage(ConfigQueryForm queryForm) {
    Page<ConfigEntity> page = PageUtil.convert2PageQuery(queryForm);
    List<ConfigEntity> entityList = configDao.queryByPage(page, queryForm);
    PageResult<ConfigVO> pageResult = PageUtil.convert2PageResult(page, entityList, ConfigVO.class);
    return HttpResponse.ok(pageResult);
  }

  /** 查询配置缓存 */
  public ConfigVO getConfig(ConfigKeyEnum configKey) {
    return this.getConfig(configKey.getValue());
  }

  /** 查询配置缓存 - 支持懒加载 */
  public ConfigVO getConfig(String configKey) {
    if (StrUtil.isBlank(configKey)) {
      return null;
    }

    // 如果缓存未初始化，则同步加载（兜底机制）
    if (!cacheInitialized.get() && CONFIG_CACHE.isEmpty()) {
      synchronized (this) {
        if (!cacheInitialized.get() && CONFIG_CACHE.isEmpty()) {
          log.info("配置缓存未初始化，执行同步加载...");
          this.loadConfigCache();
        }
      }
    }

    ConfigEntity entity = this.CONFIG_CACHE.get(configKey.toLowerCase());
    return BeanUtil.copy(entity, ConfigVO.class);
  }

  /** 查询配置缓存参数 */
  public String getConfigValue(ConfigKeyEnum configKey) {
    ConfigVO config = this.getConfig(configKey);
    return config == null ? null : config.getConfigValue();
  }

  /** 根据参数key查询 并转换为对象 */
  public <T> T getConfigValue2Obj(ConfigKeyEnum configKey, Class<T> clazz) {
    String configValue = this.getConfigValue(configKey);
    return JSON.parseObject(configValue, clazz);
  }

  /** 添加系统配置 */
  public HttpResponse<String> add(ConfigAddForm configAddForm) {
    ConfigEntity entity = configDao.selectByKey(configAddForm.getConfigKey());
    if (null != entity) {
      return HttpResponse.error(UserErrorCode.ALREADY_EXIST);
    }
    entity = BeanUtil.copy(configAddForm, ConfigEntity.class);
    configDao.insert(entity);

    // 刷新缓存
    this.refreshConfigCache(entity.getConfigId());
    return HttpResponse.ok();
  }

  /** 更新系统配置 */
  public HttpResponse<String> updateConfig(ConfigUpdateForm updateDTO) {
    Long configId = updateDTO.getConfigId();
    ConfigEntity entity = configDao.selectById(configId);
    if (null == entity) {
      return HttpResponse.error(UserErrorCode.DATA_NOT_EXIST);
    }
    ConfigEntity alreadyEntity = configDao.selectByKey(updateDTO.getConfigKey());
    if (null != alreadyEntity && !Objects.equals(configId, alreadyEntity.getConfigId())) {
      return HttpResponse.error(UserErrorCode.ALREADY_EXIST, "config key 已存在");
    }

    // 更新数据
    entity = BeanUtil.copy(updateDTO, ConfigEntity.class);
    configDao.updateById(entity);

    // 刷新缓存
    this.refreshConfigCache(configId);
    return HttpResponse.ok();
  }

  /** 更新系统配置 */
  public HttpResponse<String> updateValueByKey(ConfigKeyEnum key, String value) {
    ConfigVO config = this.getConfig(key);
    if (null == config) {
      return HttpResponse.error(UserErrorCode.DATA_NOT_EXIST);
    }

    // 更新数据
    Long configId = config.getConfigId();
    ConfigEntity entity = new ConfigEntity();
    entity.setConfigId(configId);
    entity.setConfigValue(value);
    configDao.updateById(entity);

    // 刷新缓存
    this.refreshConfigCache(configId);
    return HttpResponse.ok();
  }
}
