package com.anxys.system.support.reload.core;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import com.anxys.system.support.reload.core.annoation.Reload;
import com.anxys.system.support.reload.core.domain.ReloadObject;
import com.anxys.system.support.reload.core.thread.ReloadRunnable;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;

/**
 * ReloadManager 管理器
 *
 * <p>可以在此类中添加 检测任务 以及注册 处理程序
 */
@Slf4j
@Service
public class ReloadManager implements ApplicationContextAware {

  private static final String THREAD_NAME_PREFIX = "smart-reload";
  private static final int THREAD_COUNT = 1;

  @Value("${reload.interval-seconds}")
  private Integer intervalSeconds;

  @Resource @Lazy private AbstractReloadCommand reloadCommand;

  private final Map<String, ReloadObject> reloadObjectMap = new ConcurrentHashMap<>();

  private ScheduledThreadPoolExecutor threadPoolExecutor;

  private ApplicationContext applicationContext;

  @Override
  public void setApplicationContext(ApplicationContext applicationContext) {
    this.applicationContext = applicationContext;
  }

  @PostConstruct
  public void init() {
    if (threadPoolExecutor != null) {
      return;
    }

    // 扫描所有Bean中的@Reload注解方法
    scanReloadMethods();

    this.threadPoolExecutor =
        new ScheduledThreadPoolExecutor(
            THREAD_COUNT,
            r -> {
              Thread t = new Thread(r, THREAD_NAME_PREFIX);
              if (!t.isDaemon()) {
                t.setDaemon(true);
              }
              return t;
            });
    this.threadPoolExecutor.scheduleWithFixedDelay(
        new ReloadRunnable(this.reloadCommand), 10, this.intervalSeconds, TimeUnit.SECONDS);
    this.reloadCommand.setReloadManager(this);
  }

  @PreDestroy
  public void shutdown() {
    if (this.threadPoolExecutor != null) {
      this.threadPoolExecutor.shutdownNow();
      this.threadPoolExecutor = null;
    }
  }

  /**
   * 扫描所有Bean中的@Reload注解方法
   */
  private void scanReloadMethods() {
    String[] beanNames = applicationContext.getBeanDefinitionNames();
    for (String beanName : beanNames) {
      try {
        Object bean = applicationContext.getBean(beanName);
        Method[] methods = ReflectionUtils.getAllDeclaredMethods(bean.getClass());
        for (Method method : methods) {
          Reload reload = method.getAnnotation(Reload.class);
          if (reload == null) {
            continue;
          }
          int paramCount = method.getParameterCount();
          if (paramCount > 1) {
            log.error(
                "<<ReloadManager>> register tag reload : "
                    + reload.value()
                    + " , param count cannot greater than one !");
            continue;
          }
          String reloadTag = reload.value();
          this.register(reloadTag, new ReloadObject(bean, method));
        }
      } catch (Exception e) {
        // 忽略无法获取的Bean
        log.debug("无法获取Bean: {}, 错误: {}", beanName, e.getMessage());
      }
    }
  }

  /**
   * 注册reload
   *
   * @param tag
   * @param reloadObject
   */
  private void register(String tag, ReloadObject reloadObject) {
    if (reloadObjectMap.containsKey(tag)) {
      log.error(
          "<<ReloadManager>> register duplicated tag reload : " + tag + " , and it will be cover!");
    }
    reloadObjectMap.put(tag, reloadObject);
  }

  /**
   * 获取重载对象
   *
   * @return
   */
  public Map<String, ReloadObject> reloadObjectMap() {
    return this.reloadObjectMap;
  }
}
