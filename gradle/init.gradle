// ========================================
// Gradle 全局性能优化初始化脚本 🚀
// ========================================

allprojects {
    // 构建生命周期优化
    gradle.projectsEvaluated {
        // 禁用不必要的任务（但保留sourcesJar，因为java-common.gradle.kts中启用了它）
        tasks.matching { task ->
            task.name in ['javadoc', 'javadocJar'] &&
            !project.hasProperty('release')
        }.configureEach {
            enabled = false
        }
    }

    // 依赖下载优化 - 移除过于严格的仓库内容过滤
    // 过滤规则可能导致某些依赖无法从正确的仓库下载
    // 注释掉以避免"缺斤少两"问题
    /*
    repositories.configureEach { repo ->
        if (repo instanceof MavenArtifactRepository) {
            repo.mavenContent {
                // 只从特定仓库下载特定组的依赖
                if (repo.name.contains('aliyun')) {
                    includeGroupByRegex 'com\\.alibaba.*'
                    includeGroupByRegex 'org\\.springframework.*'
                    includeGroupByRegex 'com\\.baomidou.*'
                }
            }
        }
    }
    */

    // 任务执行优化
    tasks.configureEach { task ->
        // 为所有任务启用构建缓存
        if (task instanceof JavaCompile) {
            task.options.incremental = true
            task.options.fork = true
        }
        
        // 测试任务优化
        if (task instanceof Test) {
            task.maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
            task.forkEvery = 100
        }
    }
}


// ========================================
// 注意：仓库配置已移至 settings.gradle.kts 中的 dependencyResolutionManagement
// 这里不再配置项目级仓库，避免与 FAIL_ON_PROJECT_REPOS 模式冲突
// ========================================

// 插件仓库镜像
settingsEvaluated { settings ->
    settings.pluginManagement {
        repositories {
            maven {
                name = 'aliyun-gradle-plugin'
                url = 'https://maven.aliyun.com/repository/gradle-plugin'
            }
            maven {
                name = 'aliyun-spring-plugin'
                url = 'https://maven.aliyun.com/repository/spring-plugin'
            }
            gradlePluginPortal()
        }
    }
} 