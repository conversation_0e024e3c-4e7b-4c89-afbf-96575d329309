[versions]
# === 核心框架版本 ===
spring-boot = "3.4.5"
spring-framework = "6.2.2"
spring-ai = "1.0.0"
spring-security = "6.4.2"
spring-dependency-management = "1.1.6"

# === 构建工具版本 ===
spotless = "6.25.0"
lombok = "1.18.36"

# === 数据库相关版本 ===
mybatis-plus = "3.5.12"
postgresql = "42.7.4"
druid = "1.2.24"
h2 = "2.3.232"

# === 缓存相关版本 ===
redisson = "3.37.0"
caffeine = "3.1.8"

# === 日志相关版本 ===
log4j2 = "2.24.3"

# === 工具库版本 ===
hutool = "5.8.29"
guava = "33.4.0-jre"
commons-lang3 = "3.17.0"
commons-text = "1.12.0"
commons-collections4 = "4.4"
commons-compress = "1.27.1"
commons-io = "2.18.0"
commons-codec = "1.17.2"

# === JSON处理版本 ===
fastjson = "2.0.53"

# === 文档处理版本 ===
poi = "5.3.0"
ooxml-schemas = "1.4"
tika = "2.9.1"

# === API文档版本 ===
knife4j = "4.6.0"

# === 安全相关版本 ===
jjwt = "0.12.6"
sa-token = "1.40.0"
bouncycastle = "1.78.1"

# === 模板引擎版本 ===
velocity = "2.4.1"
velocity-tools = "3.1"
freemarker = "2.3.33"

# === 其他工具版本 ===
fast-excel = "1.2.0"
ip2region = "2.7.0"
aliyun-oss = "3.17.4"
jsoup = "1.18.1"
concurrent-linked-hashmap = "1.4.2"
hibernate-validator = "8.0.1.Final"
reflections = "0.10.2"

[libraries]
# ========================================
# BOM (Bill of Materials) - 统一版本管理
# ========================================
spring-boot-bom = { module = "org.springframework.boot:spring-boot-dependencies", version.ref = "spring-boot" }
spring-framework-bom = { module = "org.springframework:spring-framework-bom", version.ref = "spring-framework" }
spring-ai-bom = { module = "org.springframework.ai:spring-ai-bom", version.ref = "spring-ai" }

# ========================================
# Gradle 插件依赖
# ========================================
gradle-spring-boot = { module = "org.springframework.boot:spring-boot-gradle-plugin", version.ref = "spring-boot"}
gradle-spring-dependency-management = { module = "io.spring.gradle:dependency-management-plugin", version.ref = "spring-dependency-management"}
gradle-spotless = { module = "com.diffplug.spotless:spotless-plugin-gradle", version.ref = "spotless"}

# ========================================
# Spring Boot Starters - 核心功能
# ========================================
spring-boot-starter-web = { module = "org.springframework.boot:spring-boot-starter-web", version.ref = "spring-boot" }
spring-boot-starter-validation = { module = "org.springframework.boot:spring-boot-starter-validation", version.ref = "spring-boot" }
spring-boot-starter-aop = { module = "org.springframework.boot:spring-boot-starter-aop", version.ref = "spring-boot" }
spring-boot-starter-cache = { module = "org.springframework.boot:spring-boot-starter-cache", version.ref = "spring-boot" }
spring-boot-starter-mail = { module = "org.springframework.boot:spring-boot-starter-mail", version.ref = "spring-boot" }
spring-boot-starter-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator", version.ref = "spring-boot" }
spring-boot-starter-data-redis = { module = "org.springframework.boot:spring-boot-starter-data-redis", version.ref = "spring-boot" }
spring-boot-starter-testing = { module = "org.springframework.boot:spring-boot-starter-test", version.ref = "spring-boot" }

# ========================================
# Spring AI 依赖
# ========================================
spring-ai-starter-model-openai = { module = "org.springframework.ai:spring-ai-starter-model-openai", version.ref = "spring-ai" }
spring-ai-spring-boot-autoconfigure = { module = "org.springframework.ai:spring-ai-spring-boot-autoconfigure", version.ref = "spring-ai" }

# ========================================
# 开发工具
# ========================================
lombok = { module = "org.projectlombok:lombok", version.ref = "lombok" }

# ========================================
# 数据库相关
# ========================================
mybatis-plus-spring-boot3-starter = { module = "com.baomidou:mybatis-plus-spring-boot3-starter", version.ref = "mybatis-plus" }
mybatis-plus-jsqlparser = { module = "com.baomidou:mybatis-plus-jsqlparser", version.ref = "mybatis-plus" }
mybatis-plus-extension = { module = "com.baomidou:mybatis-plus-extension", version.ref = "mybatis-plus" }
postgresql = { module = "org.postgresql:postgresql", version.ref = "postgresql" }
druid-spring-boot3-starter = { module = "com.alibaba:druid-spring-boot-3-starter", version.ref = "druid" }
h2 = { module = "com.h2database:h2", version.ref = "h2" }

# ========================================
# 缓存相关
# ========================================
redisson-spring-boot-starter = { module = "org.redisson:redisson-spring-boot-starter", version.ref = "redisson" }
caffeine = { module = "com.github.ben-manes.caffeine:caffeine", version.ref = "caffeine" }

# ========================================
# 日志相关
# ========================================
log4j-slf4j2-impl = { module = "org.apache.logging.log4j:log4j-slf4j2-impl", version.ref = "log4j2" }
log4j-core = { module = "org.apache.logging.log4j:log4j-core", version.ref = "log4j2" }
log4j-api = { module = "org.apache.logging.log4j:log4j-api", version.ref = "log4j2" }

# ========================================
# 工具库
# ========================================
hutool = { module = "cn.hutool:hutool-all", version.ref = "hutool" }
guava = { module = "com.google.guava:guava", version.ref = "guava" }
commons-lang3 = { module = "org.apache.commons:commons-lang3", version.ref = "commons-lang3" }
commons-text = { module = "org.apache.commons:commons-text", version.ref = "commons-text" }
commons-collections4 = { module = "org.apache.commons:commons-collections4", version.ref = "commons-collections4" }
commons-compress = { module = "org.apache.commons:commons-compress", version.ref = "commons-compress" }
commons-io = { module = "commons-io:commons-io", version.ref = "commons-io" }
commons-codec = { module = "commons-codec:commons-codec", version.ref = "commons-codec" }
# ========================================
# JSON 处理
# ========================================
fastjson = { module = "com.alibaba:fastjson", version.ref = "fastjson" }

# ========================================
# 文档处理
# ========================================
poi = { module = "org.apache.poi:poi", version.ref = "poi" }
poi-ooxml = { module = "org.apache.poi:poi-ooxml", version.ref = "poi" }
poi-scratchpad = { module = "org.apache.poi:poi-scratchpad", version.ref = "poi" }
ooxml-schemas = { module = "org.apache.poi:ooxml-schemas", version.ref = "ooxml-schemas" }
fast-excel = { module = "cn.idev.excel:fastexcel", version.ref = "fast-excel" }
tika-core = { module = "org.apache.tika:tika-core", version.ref = "tika" }

# ========================================
# API 文档
# ========================================
knife4j-openapi3-jakarta-spring-boot-starter = { module = "com.github.xingfudeshi:knife4j-openapi3-jakarta-spring-boot-starter", version.ref = "knife4j" }

# ========================================
# 安全相关
# ========================================
jjwt-api = { module = "io.jsonwebtoken:jjwt-api", version.ref = "jjwt" }
jjwt-impl = { module = "io.jsonwebtoken:jjwt-impl", version.ref = "jjwt" }
jjwt-jackson = { module = "io.jsonwebtoken:jjwt-jackson", version.ref = "jjwt" }
sa-token-spring-boot3-starter = { module = "cn.dev33:sa-token-spring-boot3-starter", version.ref = "sa-token" }
spring-security-crypto = { module = "org.springframework.security:spring-security-crypto", version.ref = "spring-security" }
bouncycastle-provider = { module = "org.bouncycastle:bcprov-jdk18on", version.ref = "bouncycastle" }

# ========================================
# 模板引擎
# ========================================
velocity-engine-core = { module = "org.apache.velocity:velocity-engine-core", version.ref = "velocity" }
velocity-tools-generic = { module = "org.apache.velocity.tools:velocity-tools-generic", version.ref = "velocity-tools" }
freemarker = { module = "org.freemarker:freemarker", version.ref = "freemarker" }

# ========================================
# 其他工具
# ========================================
ip2region = { module = "org.lionsoul:ip2region", version.ref = "ip2region" }
aliyun-oss = { module = "com.aliyun.oss:aliyun-sdk-oss", version.ref = "aliyun-oss" }
jsoup = { module = "org.jsoup:jsoup", version.ref = "jsoup" }
concurrent-linked-hashmap = { module = "com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru", version.ref = "concurrent-linked-hashmap" }
hibernate-validator = { module = "org.hibernate.validator:hibernate-validator", version.ref = "hibernate-validator" }
reflections = { module = "org.reflections:reflections", version.ref = "reflections" }

[bundles]
# ========================================
# BOM 组合 - 便于统一导入
# ========================================
spring-boms = ["spring-boot-bom", "spring-framework-bom", "spring-ai-bom"]

# ========================================
# 功能模块组合
# ========================================
# Spring Boot 核心功能
spring-boot-core = [
    "spring-boot-starter-web",
    "spring-boot-starter-validation",
    "spring-boot-starter-aop",
    "spring-boot-starter-actuator"
]

# 数据库相关
database = [
    "mybatis-plus-spring-boot3-starter",
    "mybatis-plus-jsqlparser",
    "mybatis-plus-extension",
    "postgresql",
    "druid-spring-boot3-starter"
]

# 缓存相关
cache = [
    "spring-boot-starter-cache",
    "spring-boot-starter-data-redis",
    "redisson-spring-boot-starter",
    "caffeine"
]

# 日志相关
logging = [
    "log4j-slf4j2-impl",
    "log4j-core",
    "log4j-api"
]

# Apache Commons 工具库
commons = [
    "commons-lang3",
    "commons-text",
    "commons-collections4",
    "commons-compress",
    "commons-io",
    "commons-codec"
]

# 文档处理
document = [
    "poi",
    "poi-ooxml",
    "poi-scratchpad",
    "ooxml-schemas",
    "fast-excel",
    "tika-core"
]

# JWT 安全组件
jwt = [
    "jjwt-api",
    "jjwt-impl",
    "jjwt-jackson"
]

# 模板引擎
template = [
    "velocity-engine-core",
    "velocity-tools-generic",
    "freemarker"
]

# 测试相关
testing = [
    "spring-boot-starter-testing",
    "h2"
]


