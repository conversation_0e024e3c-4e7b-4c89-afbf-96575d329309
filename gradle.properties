# ========================================
# Gradle 8.14.2 极致性能优化配置 🚀
# 基于最新最佳实践优化
# ========================================

# === 项目信息 ===
group=com.anxys
version=3.0.0

# ========================================
# 核心性能开关 ⚡
# ========================================

# 启用Gradle守护进程 - 显著提升后续构建速度
org.gradle.daemon=true

# 并行构建 - 充分利用多核CPU
org.gradle.parallel=true

# 构建缓存 - 避免重复编译
org.gradle.caching=true

# 配置缓存 - 大幅提升构建速度（Gradle 6.6+）
org.gradle.configuration-cache=true
org.gradle.configuration-cache.problems=warn
# 忽略IDEA调试相关的系统属性，避免影响配置缓存
org.gradle.configuration-cache.inputs.unsafe.ignore.system-properties=idea.gradle.debug.all

# 文件系统监听 - 改善增量构建
org.gradle.vfs.watch=true
org.gradle.vfs.verbose=false

# ========================================
# JVM性能优化 - 适配Java 17 🔧
# ========================================

# Gradle JVM内存配置 - 使用G1GC优化
org.gradle.jvmargs=-Xmx4g -Xms1g \
  -XX:MaxMetaspaceSize=512m \
  -XX:+UseG1GC \
  -XX:+UseStringDeduplication \
  -XX:+UseCompressedOops \
  -XX:+UseCompressedClassPointers \
  -XX:G1HeapRegionSize=16m \
  -XX:MaxGCPauseMillis=100 \
  -Dfile.encoding=UTF-8 \
  -Djava.awt.headless=true

# 编译器守护进程
org.gradle.compiler.daemon=true

# ========================================
# 编译优化 🛠️
# ========================================

# 启用增量编译
org.gradle.incremental=true

# Java编译类路径打包优化
org.gradle.java.compile-classpath-packaging=true

# ========================================
# 网络和下载优化 🌐
# ========================================

# 指定HTTPS协议版本，解决TLS握手失败问题
systemProp.https.protocols=TLSv1.2

# HTTP连接超时设置
org.gradle.internal.http.connectionTimeout=60000
org.gradle.internal.http.socketTimeout=60000

# 启用HTTP连接保持
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10

# 启用并行下载
systemProp.org.gradle.internal.repository.max.concurrent.downloads=8

# 网络重试配置
systemProp.org.gradle.internal.repository.initial.backoff=500
systemProp.org.gradle.internal.repository.max.backoff=10000

# ========================================
# 依赖解析优化 📦
# ========================================

# 禁用依赖验证（开发环境）
org.gradle.dependency.verification=off

# ========================================
# 输出和日志优化 📄
# ========================================

# 控制台输出优化
org.gradle.console=rich

# 警告模式 - 只显示重要警告
org.gradle.warning.mode=summary

# ========================================
# 测试优化 🧪
# ========================================

# 测试任务并行执行
org.gradle.test.parallel=true

# 测试失败时继续执行其他测试
org.gradle.test.continueOnFailure=false

# 测试输出优化
org.gradle.test.logging.events=passed,skipped,failed,standard_error

# ========================================
# 安全和稳定性 🔒
# ========================================

# 启用本地构建缓存
org.gradle.cache.local=true

# JVM安装自动下载（禁用以提升安全性）
org.gradle.java.installations.auto-download=false

# ========================================
# Maven仓库优化（开发环境） 🏗️
# ========================================

# ========================================
# 新增优化配置 🚀
# ========================================

# 启用Gradle企业版功能（如果可用）
org.gradle.enterprise.url=

# 构建扫描配置
org.gradle.scan.termsOfServiceUrl=https://gradle.com/terms-of-service
org.gradle.scan.termsOfServiceAgree=yes

# 依赖锁定（生产环境建议启用）
org.gradle.dependency.locking=false

# 构建缓存推送（CI环境使用）
org.gradle.cache.push=false

# 启用Gradle工具链自动检测
org.gradle.java.installations.auto-detect=true

# 优化Kotlin编译
kotlin.incremental=true
kotlin.incremental.useClasspathSnapshot=true
kotlin.build.report.output=file

# 优化构建输出
org.gradle.logging.level=lifecycle

# 启用构建性能监控
org.gradle.internal.launcher.welcomeMessageEnabled=false

# ========================================
# IDEA 兼容性优化 🎯
# ========================================

# 改善 IDEA 与 Gradle 的集成
org.gradle.java.compile-classpath-packaging=true

# 确保 IDEA 使用正确的依赖解析
org.gradle.dependency.duplicate-strategy=warn

# 改善 IDEA 的增量编译
org.gradle.java.incremental.classpath.analysis=true

# IDEA 调试支持
org.gradle.debug=false
