package com.anxys.erp.stockCheck.domain;


import com.anxys.framework.common.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 库存盘点实体类
 *
 * @since 2025-06-09 16:58:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("erp_stock_check")
public class StockCheck extends BaseEntity {
    @Schema(description = "主键id")
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
            @Schema(description = "盘点单号")
            private String checkNo;
            @Schema(description = "盘点名称")
            private String checkName;
            @Schema(description = "仓库ID | erp_warehouse表的id")
            private Long warehouseId;
            @Schema(description = "仓库编码")
            private String warehouseCode;
            @Schema(description = "仓库名称")
            private String warehouseName;
            @Schema(description = "字典 | 盘点类型 check_type：FULL-全盘，PARTIAL-部分盘点，CYCLE-循环盘点，SPOT-抽盘")
            private String checkType;
            @Schema(description = "盘点日期")
                @JsonFormat(pattern = "yyyy-MM-dd")
            private LocalDate checkDate;
            @Schema(description = "字典 | 盘点状态 check_status：DRAFT-草稿，IN_PROGRESS-盘点中，COMPLETED-已完成，APPROVED-已审批，CANCELLED-已取消")
            private String checkStatus;
            @Schema(description = "字典 | 盘点范围 check_scope：ALL-全部商品，CATEGORY-按分类，PRODUCT-指定商品")
            private String checkScope;
            @Schema(description = "商品分类ID列表 (JSON格式)")
            private String productCategoryIds;
            @Schema(description = "商品ID列表 (JSON格式)")
            private String productIds;
            @Schema(description = "盘点商品总数")
            private Integer totalItems;
            @Schema(description = "已盘点商品数")
            private Integer checkedItems;
            @Schema(description = "盘盈商品数")
            private Integer profitItems;
            @Schema(description = "盘亏商品数")
            private Integer lossItems;
            @Schema(description = "盘盈总金额")
            private Double totalProfitAmount;
            @Schema(description = "盘亏总金额")
            private Double totalLossAmount;
            @Schema(description = "盘点人ID | t_employee表的employee_id")
            private Long checkerId;
            @Schema(description = "盘点人姓名")
            private String checkerName;
            @Schema(description = "盘点开始时间")
                @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime checkStartTime;
            @Schema(description = "盘点结束时间")
                @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime checkEndTime;
            @Schema(description = "审批人ID | t_employee表的employee_id")
            private Long approvalUserId;
            @Schema(description = "审批时间")
                @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime approvalTime;
            @Schema(description = "审批备注")
            private String approvalRemark;
            @Schema(description = "备注")
            private String remark;
}
