package com.anxys.erp.stockCheck.domain.form;


import com.anxys.erp.stockCheck.domain.StockCheck;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
/**
 * 库存盘点添加表单
 *
 * @since 2025-06-09 16:58:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockCheckAddParam extends StockCheck {

            @NotBlank(message = "盘点单号不能为空")
        private String checkNo;
            @NotBlank(message = "盘点名称不能为空")
        private String checkName;
            @NotNull(message = "仓库ID | erp_warehouse表的id不能为空")
        private Long warehouseId;
            @NotBlank(message = "仓库名称不能为空")
        private String warehouseName;
            @NotBlank(message = "字典 | 盘点类型 check_type：FULL-全盘，PARTIAL-部分盘点，CYCLE-循环盘点，SPOT-抽盘不能为空")
        private String checkType;
            @NotNull(message = "盘点日期不能为空")
        private LocalDate checkDate;
            @NotBlank(message = "字典 | 盘点状态 check_status：DRAFT-草稿，IN_PROGRESS-盘点中，COMPLETED-已完成，APPROVED-已审批，CANCELLED-已取消不能为空")
        private String checkStatus;
}
