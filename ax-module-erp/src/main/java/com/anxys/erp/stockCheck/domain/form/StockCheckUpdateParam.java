package com.anxys.erp.stockCheck.domain.form;


import com.anxys.erp.stockCheck.domain.StockCheck;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存盘点更新表单
 * @since 2025-06-09 16:58:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockCheckUpdateParam extends StockCheck {

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;

}
