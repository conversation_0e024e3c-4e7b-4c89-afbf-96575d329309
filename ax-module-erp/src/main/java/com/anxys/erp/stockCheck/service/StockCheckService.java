package com.anxys.erp.stockCheck.service;

import cn.hutool.core.collection.CollUtil;
import cn.idev.excel.FastExcel;
import com.anxys.erp.order.salesOrder.service.ErpNoRedisDAO;
import com.anxys.erp.stockCheck.dao.StockCheckMapper;
import com.anxys.erp.stockCheck.domain.StockCheck;
import com.anxys.erp.stockCheck.domain.excel.StockCheckExcel;
import com.anxys.erp.stockCheck.domain.form.*;
import com.anxys.erp.stockCheck.domain.vo.StockCheckVO;
import com.anxys.erp.stockCheckItem.dao.StockCheckItemMapper;
import com.anxys.erp.stockCheckItem.domain.StockCheckItem;
import com.anxys.erp.stockCheckItem.domain.form.StockCheckItemAddParam;
import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import com.anxys.framework.common.utils.object.BeanUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import static com.anxys.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.anxys.framework.common.utils.collection.CollectionUtils.convertList;

/**
 * 库存盘点服务层
 *
 * @since 2025-06-09 16:58:50
 */
@Slf4j
@Service
public class StockCheckService {

    @Resource
    private StockCheckMapper stockCheckMapper;

    @Resource
    private ErpNoRedisDAO noRedisDAO;

    @Resource
    private StockCheckItemMapper stockCheckItemMapper;

    /**
     * 分页查询
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    public PageResult<StockCheckVO> page(StockCheckPageParam queryParam) {
        Page<?> page = PageUtil.convert2PageQuery(queryParam.getPageParam());
        List<StockCheckVO> PageVo = stockCheckMapper.page(page, queryParam);
        return PageUtil.convert2PageResult(page, PageVo);
    }

    /**
     * 列表查询
     *
     * @param queryParam 查询参数
     * @return 列表结果
     */
    public List<StockCheckVO> list(StockCheckQueryParam queryParam) {
        return stockCheckMapper.list(queryParam);
    }

    /**
     * 添加
     *
     * @param addParam 添加参数
     * @return 新创建的对象VO
     */
    @Transactional(rollbackFor = Exception.class)
    public StockCheckVO insert(StockCheckAddParam addParam) {
        StockCheck stockCheck = BeanUtil.copy(addParam, StockCheck.class);
        stockCheckMapper.insert(stockCheck);
        // 返回新创建的对象
        return BeanUtil.copy(stockCheck, StockCheckVO.class);
    }

    /**
     * 更新
     *
     * @param updateParam 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(StockCheckUpdateParam updateParam) {
        StockCheck stockCheck = BeanUtil.copy(updateParam, StockCheck.class);
        stockCheckMapper.updateById(stockCheck);
    }

    /**
     * 批量删除
     *
     * @param idList ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        if (!idList.isEmpty()) {
            stockCheckMapper.deleteByIds(idList, true);
        }
    }

    /**
     * 单个删除
     *
     * @param id ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        stockCheckMapper.deleteById(id);
    }

    /**
     * 通过ID查询
     *
     * @param id ID
     * @return 详情
     */
    public StockCheckVO queryById(Long id) {
        StockCheck stockCheck = stockCheckMapper.selectById(id);
        if (stockCheck == null) {
            throw new BusinessException(UserErrorCode.DATA_NOT_EXIST);
        }
        return BeanUtil.copy(stockCheck, StockCheckVO.class);
    }

    /**
     * 导出Excel
     *
     * @param queryParam 查询参数
     * @return Excel数据列表
     */
    public List<StockCheckExcel> exportExcel(StockCheckQueryParam queryParam) {
        return stockCheckMapper.exportExcel(queryParam);
    }


    /**
     * 导入Excel
     *
     * @param file Excel文件
     * @return 导入的数据条数
     */
    @Transactional(rollbackFor = Exception.class)
    public int importExcel(MultipartFile file) {
        List<StockCheckExcel> dataList;
        try {
            dataList = FastExcel.read(file.getInputStream())
                    .head(StockCheckExcel.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            log.error("Excel导入失败", e);
            throw new BusinessException("数据格式存在问题，无法读取");
        }

        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException("数据为空");
        }

        // 处理导入数据
        log.info("开始处理导入数据，共 {} 条", dataList.size());
        List<StockCheck> entityList = dataList.stream()
                .map(excel -> {
                    log.info("处理数据: {}", excel);
                    return BeanUtil.copy(excel, StockCheck.class);
                })
                .toList();

        stockCheckMapper.batchInsert(entityList);

        return dataList.size();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createStockCheck(StockCheckSaveParam saveParam) {
        // 1.1 校验盘点项的有效性
        List<StockCheckItemAddParam> stockCheckItems = validateStockCheckItems(saveParam.getItems());

        // 1.2 生成盘点单号，并校验唯一性
        String no = noRedisDAO.generate(ErpNoRedisDAO.STOCK_CHECK_NO_PREFIX);
        if (stockCheckMapper.selectByCheckNo(no) != null) {
            throw exception("生成盘点号失败，请重新提交");
        }

        // 2.1 插入盘点单
        StockCheck stockCheck = BeanUtils.toBean(saveParam, StockCheck.class);
        stockCheck.setCheckNo(no);
        stockCheck.setTotalItems(calculateTotalItems(stockCheckItems));
        stockCheck.setTotalProfitAmount(calculateTotalProfitAmount(stockCheckItems).doubleValue());
        stockCheck.setTotalLossAmount(calculateTotalLossAmount(stockCheckItems).doubleValue());

        int insertResult = stockCheckMapper.insert(stockCheck);
        if (insertResult <= 0 || stockCheck.getId() == null) {
            throw new BusinessException("创建库存盘点单失败");
        }

        // 2.2 插入盘点单项
        stockCheckItems.forEach(o -> {
            o.setCheckId(stockCheck.getId());
            o.setCheckNo(stockCheck.getCheckNo()); // 设置盘点单号
        });
        List<StockCheckItem> stockCheckItemList = stockCheckItems.stream()
                .map(item -> BeanUtil.copy(item, StockCheckItem.class))
                .toList();
        stockCheckItemMapper.batchInsert(stockCheckItemList);

        return stockCheck.getId();
    }


    /**
     * 校验盘点项的有效性
     *
     * @param updateParam 盘点项列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStockCheck(StockCheckSaveParam updateParam) {
        // 1.1 校验存在
        StockCheck stockCheck = validateStockCheckExists(updateParam.getCheckNo());

        // 1.2 校验盘点项的有效性
        List<StockCheckItemAddParam> stockCheckItems = validateStockCheckItems(updateParam.getItems());

        // 2.1 更新盘点单
        StockCheck updateObj = BeanUtils.toBean(updateParam, StockCheck.class);
        updateObj.setTotalItems(calculateTotalItems(stockCheckItems));
        updateObj.setTotalProfitAmount(calculateTotalProfitAmount(stockCheckItems).doubleValue());
        updateObj.setTotalLossAmount(calculateTotalLossAmount(stockCheckItems).doubleValue());
        stockCheckMapper.updateById(updateObj);
        // 2.2 更新盘点单项
        updateStockCheckItemList(stockCheck.getId(), stockCheckItems);
    }
    /**
     * 校验盘点项的有效性
     *
     * @param items 盘点项列表
     * @return 转换后的盘点项列表
     */
    private List<StockCheckItemAddParam> validateStockCheckItems(List<StockCheckSaveParam.Item> items) {
        if (CollectionUtils.isEmpty(items)) {
            throw new BusinessException("盘点明细项不能为空");
        }

        // 转换为StockCheckItemAddParam列表
        return convertList(items, item -> {
            StockCheckItemAddParam addParam = BeanUtils.toBean(item, StockCheckItemAddParam.class);

            // 转换BigDecimal到Double
            addParam.setBookStock(item.getBookStock() != null ? item.getBookStock().doubleValue() : 0.0);
            addParam.setActualStock(item.getActualStock() != null ? item.getActualStock().doubleValue() : null);
            addParam.setDifferenceStock(item.getDifferenceStock() != null ? item.getDifferenceStock().doubleValue() : null);
            addParam.setUnitCost(item.getUnitCost() != null ? item.getUnitCost().doubleValue() : null);

            // 计算金额相关字段
            Double bookStock = addParam.getBookStock();
            Double actualStock = addParam.getActualStock();
            Double unitCost = addParam.getUnitCost();

            if (bookStock != null && unitCost != null) {
                addParam.setBookAmount(bookStock * unitCost);
            }

            if (actualStock != null && unitCost != null) {
                addParam.setActualAmount(actualStock * unitCost);
            }

            if (addParam.getBookAmount() != null && addParam.getActualAmount() != null) {
                addParam.setDifferenceAmount(addParam.getActualAmount() - addParam.getBookAmount());
            }

            return addParam;
        });
    }

    /**
     * 计算盘点商品总数
     */
    private Integer calculateTotalItems(List<StockCheckItemAddParam> items) {
        return items.size();
    }

    private StockCheck validateStockCheckExists(String id) {
        StockCheck stockCheck = stockCheckMapper.selectByCheckNo(id);
        if (stockCheck == null) {
            throw exception("库存盘点单不存在");
        }
        return stockCheck;
    }

    private void updateStockCheckItemList(Long checkId, List<StockCheckItemAddParam> newList) {
        // 第一步，删除原有的盘点明细项（逻辑删除）
        stockCheckItemMapper.delete(new LambdaQueryWrapper<StockCheckItem>()
                .eq(StockCheckItem::getCheckId, checkId));

        // 第二步，批量插入新的盘点明细项
        if (CollUtil.isNotEmpty(newList)) {
            // 获取盘点单号
            StockCheck stockCheck = stockCheckMapper.selectById(checkId);
            String checkNo = stockCheck != null ? stockCheck.getCheckNo() : null;

            newList.forEach(item -> {
                item.setCheckId(checkId);
                item.setCheckNo(checkNo); // 设置盘点单号
            });
            List<StockCheckItem> stockCheckItemList = newList.stream()
                    .map(item -> BeanUtil.copy(item, StockCheckItem.class))
                    .toList();
            stockCheckItemMapper.batchInsert(stockCheckItemList);
        }
    }

    /**
     * 计算盘盈总金额
     */
    private BigDecimal calculateTotalProfitAmount(List<StockCheckItemAddParam> items) {
        return items.stream()
                .filter(item -> item.getDifferenceAmount() != null && item.getDifferenceAmount() > 0)
                .map(item -> BigDecimal.valueOf(item.getDifferenceAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算盘亏总金额
     */
    private BigDecimal calculateTotalLossAmount(List<StockCheckItemAddParam> items) {
        return items.stream()
                .filter(item -> item.getDifferenceAmount() != null && item.getDifferenceAmount() < 0)
                .map(item -> BigDecimal.valueOf(Math.abs(item.getDifferenceAmount())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
