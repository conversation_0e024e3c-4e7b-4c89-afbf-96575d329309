package com.anxys.erp.stockCheck.domain.excel;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 库存盘点Excel导出VO
 * @since 2025-06-09 16:58:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockCheckExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

@ExcelProperty("盘点单号")        private String checkNo;
@ExcelProperty("盘点名称")        private String checkName;
@ExcelProperty("仓库ID | erp_warehouse表的id")        private Long warehouseId;
@ExcelProperty("仓库编码")        private String warehouseCode;
@ExcelProperty("仓库名称")        private String warehouseName;
@ExcelProperty("字典 | 盘点类型 check_type：FULL-全盘，PARTIAL-部分盘点，CYCLE-循环盘点，SPOT-抽盘")        private String checkType;
@ExcelProperty("盘点日期")        private LocalDate checkDate;
@ExcelProperty("字典 | 盘点状态 check_status：DRAFT-草稿，IN_PROGRESS-盘点中，COMPLETED-已完成，APPROVED-已审批，CANCELLED-已取消")        private String checkStatus;
@ExcelProperty("字典 | 盘点范围 check_scope：ALL-全部商品，CATEGORY-按分类，PRODUCT-指定商品")        private String checkScope;
@ExcelProperty("商品分类ID列表 (JSON格式)")        private String productCategoryIds;
@ExcelProperty("商品ID列表 (JSON格式)")        private String productIds;
@ExcelProperty("盘点商品总数")        private Integer totalItems;
@ExcelProperty("已盘点商品数")        private Integer checkedItems;
@ExcelProperty("盘盈商品数")        private Integer profitItems;
@ExcelProperty("盘亏商品数")        private Integer lossItems;
@ExcelProperty("盘盈总金额")        private Double totalProfitAmount;
@ExcelProperty("盘亏总金额")        private Double totalLossAmount;
@ExcelProperty("盘点人ID | t_employee表的employee_id")        private Long checkerId;
@ExcelProperty("盘点人姓名")        private String checkerName;
@ExcelProperty("盘点开始时间")        private LocalDateTime checkStartTime;
@ExcelProperty("盘点结束时间")        private LocalDateTime checkEndTime;
@ExcelProperty("审批人ID | t_employee表的employee_id")        private Long approvalUserId;
@ExcelProperty("审批时间")        private LocalDateTime approvalTime;
@ExcelProperty("审批备注")        private String approvalRemark;
@ExcelProperty("备注")        private String remark;
@ExcelProperty("创建人")        private Long createdBy;
@ExcelProperty("更新人")        private Long updatedBy;
@ExcelProperty("创建时间")        private LocalDateTime createTime;
@ExcelProperty("更新时间")        private LocalDateTime updateTime;
}
