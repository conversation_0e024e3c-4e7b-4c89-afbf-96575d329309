package com.anxys.erp.stockCheck.domain.form;


import com.anxys.erp.stockCheck.domain.StockCheck;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 库存盘点查询表单
 *
 * @since 2025-06-09 16:58:50
 */
@EqualsAndHashCode(callSuper = false)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class StockCheckQueryParam extends StockCheck {

            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "盘点日期始")
            private LocalDate checkDateFrom;

            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "盘点日期至")
            private LocalDate checkDateTo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "盘点开始时间始")
            private LocalDateTime checkStartTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "盘点开始时间至")
            private LocalDateTime checkStartTimeTo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "盘点结束时间始")
            private LocalDateTime checkEndTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "盘点结束时间至")
            private LocalDateTime checkEndTimeTo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "审批时间始")
            private LocalDateTime approvalTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "审批时间至")
            private LocalDateTime approvalTimeTo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "创建时间始")
            private LocalDateTime createTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "创建时间至")
            private LocalDateTime createTimeTo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "更新时间始")
            private LocalDateTime updateTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "更新时间至")
            private LocalDateTime updateTimeTo;
}
