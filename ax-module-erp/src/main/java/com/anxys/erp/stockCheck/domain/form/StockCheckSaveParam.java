package com.anxys.erp.stockCheck.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - ERP 库存盘点新增/修改 Request VO")
@Data
public class StockCheckSaveParam {

    @Schema(description = "盘点ID", example = "11756")
    private Long id;

    @Schema(description = "盘点单号", example = "PC202412010001")
    private String checkNo;

    @Schema(description = "盘点名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024年12月仓库盘点")
    @NotNull(message = "盘点名称不能为空")
    private String checkName;

    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3113")
    @NotNull(message = "仓库ID不能为空")
    private Long warehouseId;

    @Schema(description = "仓库编码", example = "WH001")
    private String warehouseCode;

    @Schema(description = "仓库名称", example = "总仓库")
    private String warehouseName;

    @Schema(description = "盘点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "FULL")
    @NotNull(message = "盘点类型不能为空")
    private String checkType;

    @Schema(description = "盘点日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "盘点日期不能为空")
    private LocalDate checkDate;

    @Schema(description = "盘点状态", example = "DRAFT")
    private String checkStatus;

    @Schema(description = "盘点范围", example = "ALL")
    private String checkScope;

    @Schema(description = "商品分类ID列表", example = "[1,2,3]")
    private String productCategoryIds;

    @Schema(description = "商品ID列表", example = "[101,102,103]")
    private String productIds;

    @Schema(description = "盘点人ID", example = "1001")
    private Long checkerId;

    @Schema(description = "盘点人姓名", example = "张三")
    private String checkerName;

    @Schema(description = "盘点开始时间")
    private LocalDateTime checkStartTime;

    @Schema(description = "盘点结束时间")
    private LocalDateTime checkEndTime;

    @Schema(description = "审批人ID", example = "1002")
    private Long approvalUserId;

    @Schema(description = "审批时间")
    private LocalDateTime approvalTime;

    @Schema(description = "审批备注", example = "审批通过")
    private String approvalRemark;

    @Schema(description = "备注", example = "年度库存盘点")
    private String remark;

    @Schema(description = "盘点明细项列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "盘点明细项列表不能为空")
    @Valid
    private List<Item> items;

    @Data
    public static class Item {

        @Schema(description = "明细ID", example = "11756")
        private Long id;

        @Schema(description = "行号", example = "1")
        private Integer lineNo;

        @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3113")
        @NotNull(message = "仓库ID不能为空")
        private Long warehouseId;

        @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3113")
        @NotNull(message = "商品ID不能为空")
        private Long productId;

        @Schema(description = "商品编码", example = "P001")
        private String productCode;

        @Schema(description = "商品名称", example = "iPhone 15")
        private String productName;

        @Schema(description = "SKU ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5001")
        @NotNull(message = "SKU ID不能为空")
        private Long skuId;

        @Schema(description = "SKU编码", example = "SKU001")
        private String skuCode;

        @Schema(description = "规格摘要", example = "黑色 128GB")
        private String specSummary;

        @Schema(description = "单位", example = "台")
        private String unit;

        @Schema(description = "账面库存", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.000")
        @NotNull(message = "账面库存不能为空")
        private BigDecimal bookStock;

        @Schema(description = "实盘库存", example = "98.000")
        private BigDecimal actualStock;

        @Schema(description = "差异数量", example = "-2.000")
        private BigDecimal differenceStock;

        @Schema(description = "差异类型", example = "LOSS")
        private String differenceType;

        @Schema(description = "单位成本", example = "5000.0000")
        private BigDecimal unitCost;

        @Schema(description = "账面金额", example = "500000.00")
        private BigDecimal bookAmount;

        @Schema(description = "实盘金额", example = "490000.00")
        private BigDecimal actualAmount;

        @Schema(description = "差异金额", example = "-10000.00")
        private BigDecimal differenceAmount;

        @Schema(description = "盘点状态", example = "PENDING")
        private String checkStatus;

        @Schema(description = "是否已盘点", example = "false")
        private Boolean isChecked;

        @Schema(description = "盘点时间")
        private LocalDateTime checkTime;

        @Schema(description = "盘点人ID", example = "1001")
        private Long checkerId;

        @Schema(description = "盘点人姓名", example = "张三")
        private String checkerName;

        @Schema(description = "差异原因", example = "DAMAGE")
        private String differenceReason;

        @Schema(description = "备注", example = "商品包装损坏")
        private String remark;

    }

}
