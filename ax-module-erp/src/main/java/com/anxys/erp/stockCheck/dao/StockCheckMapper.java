package com.anxys.erp.stockCheck.dao;


import com.anxys.erp.stockCheck.domain.StockCheck;
import com.anxys.erp.stockCheck.domain.excel.StockCheckExcel;
import com.anxys.erp.stockCheck.domain.form.StockCheckPageParam;
import com.anxys.erp.stockCheck.domain.form.StockCheckQueryParam;
import com.anxys.erp.stockCheck.domain.vo.StockCheckVO;
import com.anxys.framework.mybatis.mapper.BaseMapperX;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存盘点数据库访问层
 * @since 2025-06-09 16:58:50
 *
 */
@Mapper
public interface StockCheckMapper extends BaseMapperX<StockCheck> {

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param queryParam 查询参数
     * @return 对象列表
     */
    List<StockCheckVO> page(Page page, @Param("param") StockCheckPageParam queryParam);


    /**
    * 查询列表
    * @param queryParam 查询参数
    * @return 对象列表
    *
    */
    List<StockCheckVO> list(@Param("param") StockCheckQueryParam queryParam);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList") List<Long> idList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 批量插入
     */
    void batchInsert(List<StockCheck> entityList);


    /**
     * 根据ID删除
     *
     * @param id ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);


    /**
     * 导出Excel
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<StockCheckExcel> exportExcel(@Param("query") StockCheckQueryParam queryParam);

    /**
     * 根据盘点单号查询
     *
     * @param checkNo 盘点单号
     * @return 盘点记录
     */
    StockCheck selectByCheckNo(@Param("checkNo") String checkNo);
}
