package com.anxys.erp.stockCheck.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.anxys.erp.stockCheck.domain.excel.StockCheckExcel;
import com.anxys.erp.stockCheck.domain.form.*;
import com.anxys.erp.stockCheck.domain.vo.StockCheckVO;
import com.anxys.erp.stockCheck.service.StockCheckService;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.framework.common.utils.DateFormatterEnum;
import com.anxys.framework.common.utils.ExcelUtil;
import com.anxys.framework.common.utils.LocalDateUtil;
import com.anxys.system.utils.AdminRequestUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存盘点控制器
 *
 * @since 2025-06-09 16:58:50
 */
@Slf4j
@RestController
@RequestMapping("/stockCheck")
@Tag(name = "库存盘点接口")
public class StockCheckController {

    @Resource
    private StockCheckService stockCheckService;

    @Operation(summary = "库存盘点分页查询")
    @PostMapping("/stockCheckPage")
    @SaCheckPermission("stock-check:query")
    public HttpResponse<PageResult<StockCheckVO>> query(@RequestBody @Valid StockCheckPageParam queryParam) {
        PageResult<StockCheckVO> pageResult = stockCheckService.page(queryParam);
        return HttpResponse.ok(pageResult);
    }

    @Operation(summary = "库存盘点列表查询")
    @PostMapping("/stockCheckList")
    @SaCheckPermission("stock-check:query")
    public HttpResponse<List<StockCheckVO>> list(@RequestBody @Valid StockCheckQueryParam queryParam) {
        List<StockCheckVO> list = stockCheckService.list(queryParam);
        return HttpResponse.ok(list);
    }

    @Operation(summary = "获取库存盘点详情")
    @GetMapping("/stockCheckDetail/{id}")
    @SaCheckPermission("stock-check:query")
    public HttpResponse<StockCheckVO> detail(@PathVariable Long id) {
        StockCheckVO stockCheckVO = stockCheckService.queryById(id);
        return HttpResponse.ok(stockCheckVO);
    }

    @Operation(summary = "添加库存盘点")
    @PostMapping("/addStockCheck")
    @SaCheckPermission("stock-check:add")
    public HttpResponse<StockCheckVO> insert(@RequestBody @Valid StockCheckAddParam addParam) {
        StockCheckVO result = stockCheckService.insert(addParam);
        return HttpResponse.ok(result);
    }

    @Operation(summary = "更新库存盘点")
    @PostMapping("/updateStockCheck")
    @SaCheckPermission("stock-check:update")
    public HttpResponse<String> update(@RequestBody @Valid StockCheckUpdateParam updateParam) {
        stockCheckService.update(updateParam);
        return HttpResponse.ok();
    }

    @Operation(summary = "删除库存盘点")
    @GetMapping("/deleteStockCheck/{id}")
    @SaCheckPermission("stock-check:delete")
    public HttpResponse<String> delete(@PathVariable Long id) {
        stockCheckService.delete(id);
        return HttpResponse.ok();
    }

    @Operation(summary = "批量删除库存盘点")
    @PostMapping("/batchDeleteStockCheck")
    @SaCheckPermission("stock-check:delete")
    public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
        stockCheckService.batchDelete(idList);
        return HttpResponse.ok();
    }

    @Operation(summary = "导出库存盘点Excel")
    @GetMapping("/exportStockCheckExcel")
    @SaCheckPermission("stock-check:export")
    public void exportExcel(@Parameter(description = "查询参数") StockCheckQueryParam queryParam, HttpServletResponse response) throws IOException {
        List<StockCheckExcel> list = stockCheckService.exportExcel(queryParam);
        // 添加水印
        String watermark = AdminRequestUtil.getRequestUser().getActualName();
        watermark += LocalDateUtil.format(LocalDateTime.now(), DateFormatterEnum.YMD_HMS);

        ExcelUtil.exportExcelWithWatermark(response, "库存盘点列表.xlsx", "库存盘点", StockCheckExcel.class, list, watermark);
    }

    @Operation(summary = "导入库存盘点Excel")
    @PostMapping("/importStockCheckExcel")
    @SaCheckPermission("stock-check:import")
    public HttpResponse<String> importExcel(@RequestParam("file") MultipartFile file) {
        int count = stockCheckService.importExcel(file);
        return HttpResponse.okMsg("成功导入" + count + "条数据");
    }

    @Operation(summary = "创建库存盘点单（包含明细）")
    @PostMapping("/createStockCheck")
    @SaCheckPermission("stock-check:add")
    public HttpResponse<Long> createStockCheck(@RequestBody @Valid StockCheckSaveParam saveParam) {
        Long stockCheckId = stockCheckService.createStockCheck(saveParam);
        if (stockCheckId == null) {
            throw new BusinessException("创建库存盘点单失败");
        }
        return HttpResponse.ok(stockCheckId);
    }

    @Operation(summary = "更新库存盘点单（包含明细）")
    @PostMapping("/updateStockCheckWithItems")
    @SaCheckPermission("stock-check:update")
    public HttpResponse<String> updateStockCheckWithItems(@RequestBody @Valid StockCheckSaveParam updateParam) {
        stockCheckService.updateStockCheck(updateParam);
        return HttpResponse.ok();
    }
}
