package com.anxys.erp.supplier.domain.form;

import com.anxys.erp.supplier.domain.Supplier;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 供应商添加表单
 *
 * @since 2025-03-18 23:08:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierAddParam extends Supplier {

  @NotBlank(message = "供应商名称不能为空")
  private String supplierName;

  @NotBlank(message = "供应商类型：material-原材料供应商，equipment-设备供应商，service-服务供应商不能为空")
  private String supplierType;

  @NotBlank(message = "状态：active-活跃，inactive-非活跃，blacklisted-黑名单不能为空")
  private String status;

  @Schema(description = "地址信息列表")
  @NotEmpty(message = "地址信息列表不能为空")
  @Valid
  private List<Map<String, Object>> addressInfoList;

  @Schema(description = "支付信息列表")
  @NotEmpty(message = "支付信息列表不能为空")
  @Valid
  private List<Map<String, Object>> paymentInfoList;
}
