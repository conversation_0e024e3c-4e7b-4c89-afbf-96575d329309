package com.anxys.erp.paramJson.domain.ParamEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierConfig extends BaseConfig{

    @Schema(description = "折扣率")
    private Double discountRate;

    @Schema(description = "税率")
    private Double taxRate;

    @Schema(description = "加工费")
    private List<SupplierProcessingFee> processingFee;

    @Data
    public static class SupplierProcessingFee {
        @Schema(description = "层数")
        private Integer layer;

        @Schema(description = "加工费")
        private Double fee;

        @Schema(description = "单位")
        private String unit;
    }
}
