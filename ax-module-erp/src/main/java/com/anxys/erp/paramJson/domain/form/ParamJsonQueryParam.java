package com.anxys.erp.paramJson.domain.form;

import com.anxys.erp.paramJson.domain.ParamJson;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * json参数查询表单
 *
 * @since 2025-04-01 00:35:28
 */
@EqualsAndHashCode(callSuper = false)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ParamJsonQueryParam extends ParamJson {

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "创建时间始")
            private LocalDateTime createTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "创建时间至")
            private LocalDateTime createTimeTo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "更新时间始")
            private LocalDateTime updateTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "更新时间至")
            private LocalDateTime updateTimeTo;
}
