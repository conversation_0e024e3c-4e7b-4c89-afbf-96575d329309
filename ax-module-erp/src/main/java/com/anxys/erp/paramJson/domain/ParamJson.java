package com.anxys.erp.paramJson.domain;

import com.anxys.framework.common.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * json参数实体类
 *
 * @since 2025-04-01 00:35:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("erp_param_json")
public class ParamJson extends BaseEntity {
    @Schema(description = "主键id")
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
    @Schema(description = "参数json")
    private String paramJson;
    @Schema(description = "关系类型")
    private String relationType;
    @Schema(description = "关系id json对应的关系对象的id")
    private Long relationId;
    @Schema(description = "记录数据结构的json的id")
    private Long sourceId;
}
