package com.anxys.erp.paramJson.dao;

import com.anxys.erp.paramJson.domain.ParamJson;
import com.anxys.erp.paramJson.domain.excel.ParamJsonExcel;
import com.anxys.erp.paramJson.domain.form.ParamJsonPageParam;
import com.anxys.erp.paramJson.domain.form.ParamJsonQueryParam;
import com.anxys.erp.paramJson.domain.vo.ParamJsonVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * json参数数据库访问层
 * @since 2025-04-01 00:35:28
 */
@Mapper
public interface ParamJsonMapper extends BaseMapper<ParamJson> {

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param queryParam 查询参数
     * @return 对象列表
     */
    List<ParamJsonVO> page(Page page, @Param("param") ParamJsonPageParam queryParam);

  /**
  * 查询列表
  * @param queryParam 查询参数
  * @return 对象列表
  *
  */
    List<ParamJsonVO> list(@Param("param") ParamJsonQueryParam queryParam);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList") List<Long> idList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 根据ID删除
     *
     * @param id ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);


    /**
     * 导出Excel
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<ParamJsonExcel> exportExcel(@Param("query") ParamJsonQueryParam queryParam);
}
