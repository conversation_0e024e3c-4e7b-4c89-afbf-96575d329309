package com.anxys.erp.paramJson.domain.ParamEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "参数配置基类")
public class BaseConfig {

    @Schema(description = "配置ID")
    private Long id;

    @Schema(description = "参数编码")
    private String code;        // 参数编码，唯一标识

    @Schema(description = "参数名称")
    private String name;        // 参数名称，用于显示

    @Schema(description = "参数描述")
    private String description; // 参数描述

    @Schema(description = "排序号")
    private Integer orderNum;   // 排序序号

    @Schema(description = "分组编码")
    private String groupCode;

    @Schema(description = "关系类型")
    private String relationType;

    @Schema(description = "关系ID")
    private Long relationId;
}
