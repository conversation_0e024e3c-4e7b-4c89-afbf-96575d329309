package com.anxys.erp.paramJson.domain.excel;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * json参数Excel导出VO
 * @since 2025-04-01 00:35:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParamJsonExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

@ExcelProperty("参数json")        private String paramJson;
@ExcelProperty("关系类型")        private String relationType;
@ExcelProperty("关系id json对应的关系对象的id")        private Long relationId;
@ExcelProperty("记录数据结构的json的id")        private Long sourceId;
@ExcelProperty("创建人")        private Long createdBy;
@ExcelProperty("更新人")        private Long updatedBy;
@ExcelProperty("创建时间")        private LocalDateTime createTime;
@ExcelProperty("更新时间")        private LocalDateTime updateTime;
}
