package com.anxys.erp.paramJson.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.anxys.erp.paramJson.domain.ParamEntity.NumberConfig;
import com.anxys.erp.paramJson.domain.ParamEntity.SupplierConfig;
import com.anxys.erp.paramJson.domain.form.ParamJsonAddParam;
import com.anxys.erp.paramJson.domain.form.ParamJsonPageParam;
import com.anxys.erp.paramJson.domain.form.ParamJsonQueryParam;
import com.anxys.erp.paramJson.domain.form.ParamJsonUpdateParam;
import com.anxys.erp.paramJson.domain.vo.ParamJsonVO;
import com.anxys.erp.paramJson.service.ParamJsonService;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * json参数控制器
 *
 * @since 2025-04-01 00:35:28
 */
@Slf4j
@RestController
@RequestMapping("/paramJson")
@Tag(name = "json参数接口")
public class ParamJsonController {

    @Resource
    private ParamJsonService paramJsonService;

    @Operation(summary = "json参数分页查询")
    @PostMapping("/paramJsonPage")
    @SaCheckPermission("param-json:query")
    public HttpResponse<PageResult<ParamJsonVO>> query(@RequestBody @Valid ParamJsonPageParam queryParam) {
        PageResult<ParamJsonVO> pageResult = paramJsonService.page(queryParam);
        return HttpResponse.ok(pageResult);
    }

    @Operation(summary = "json参数列表查询")
    @PostMapping("/paramJsonList")
    @SaCheckPermission("param-json:query")
    public HttpResponse<List<ParamJsonVO>> list(@RequestBody @Valid ParamJsonQueryParam queryParam) {
        List<ParamJsonVO> list = paramJsonService.list(queryParam);
        return HttpResponse.ok(list);
    }

    @Operation(summary = "获取json参数详情")
    @GetMapping("/paramJsonDetail/{id}")
    @SaCheckPermission("param-json:query")
    public HttpResponse<ParamJsonVO> detail(@PathVariable Long id) {
        ParamJsonVO paramJsonVO = paramJsonService.queryById(id);
        return HttpResponse.ok(paramJsonVO);
    }

    @Operation(summary = "获取纸箱报价参数")
    @GetMapping("/getBoxQuotationParam")
    @SaCheckPermission("param-json:query")
    public HttpResponse<List<NumberConfig>> getBoxQuotationParam() {
        return HttpResponse.ok(paramJsonService.getBoxQuotationConfigParam());
    }

    @Operation(summary = "更新纸箱报价参数")
    @PostMapping("/updateBoxQuotationParam")
    @SaCheckPermission("param-json:update")
    public HttpResponse<String> updateBoxQuotationParam(@RequestBody @Valid List<NumberConfig> paramList) {
        paramJsonService.updateBoxQuotationParam(paramList);
        return HttpResponse.ok();
    }

    @Operation(summary = "获取供应商配置")
    @GetMapping("/getSupplierConfig/{supplierId}")
    @SaCheckPermission("param-json:query")
    public HttpResponse<SupplierConfig> getSupplierConfig(@PathVariable Long supplierId) {
        return HttpResponse.ok(paramJsonService.getSupplierConfig(supplierId));
    }

    @Operation(summary = "更新供应商配置")
    @PostMapping("/updateSupplierConfig")
    @SaCheckPermission("param-json:update")
    public HttpResponse<String> updateSupplierConfig(@RequestBody @Valid SupplierConfig supplierConfig) {
        paramJsonService.updateSupplierConfig(supplierConfig);
        return HttpResponse.ok();
    }

    @Operation(summary = "添加json参数")
    @PostMapping("/addParamJson")
    @SaCheckPermission("param-json:add")
    public HttpResponse<String> insert(@RequestBody @Valid ParamJsonAddParam addParam) {
        paramJsonService.insert(addParam);
        return HttpResponse.ok();
    }

    @Operation(summary = "更新json参数")
    @PostMapping("/updateParamJson")
    @SaCheckPermission("param-json:update")
    public HttpResponse<String> update(@RequestBody @Valid ParamJsonUpdateParam updateParam) {
        paramJsonService.update(updateParam);
        return HttpResponse.ok();
    }

    @Operation(summary = "删除json参数")
    @GetMapping("/deleteParamJson/{id}")
    @SaCheckPermission("param-json:delete")
    public HttpResponse<String> delete(@PathVariable Long id) {
        paramJsonService.delete(id);
        return HttpResponse.ok();
    }

    @Operation(summary = "批量删除json参数")
    @PostMapping("/batchDeleteParamJson")
    @SaCheckPermission("param-json:delete")
    public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
        paramJsonService.batchDelete(idList);
        return HttpResponse.ok();
    }

}
