package com.anxys.erp.paramJson.domain.ParamEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "数值类型参数配置")
public class NumberConfig extends BaseConfig {

    @Schema(description = "参数值")
    private Double value;

    @Schema(description = "步进值")
    private Double step;

    @Schema(description = "最小值")
    private Double min;

    @Schema(description = "最大值")
    private Double max;

    @Schema(description = "小数位数")
    private Integer decimalPlaces;
}
