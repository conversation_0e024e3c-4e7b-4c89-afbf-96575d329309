package com.anxys.erp.paramJson.domain.form;

import com.anxys.erp.paramJson.domain.ParamJson;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * json参数更新表单
 *
 * @since 2025-04-01 00:35:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ParamJsonUpdateParam extends ParamJson {

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;


}
