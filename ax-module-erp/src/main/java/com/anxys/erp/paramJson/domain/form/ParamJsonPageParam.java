package com.anxys.erp.paramJson.domain.form;

import com.anxys.framework.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * json参数分页查询表单
 *
 * @since 2025-04-01 00:35:28
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class ParamJsonPageParam extends ParamJsonQueryParam {

    @Schema(description = "分页参数")
    @Valid
    @NotNull(message = "分页参数不能为空")
    PageParam pageParam = new PageParam();

}
