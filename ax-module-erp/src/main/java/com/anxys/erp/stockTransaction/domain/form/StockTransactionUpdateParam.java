 package com.anxys.erp.stockTransaction.domain.form;


import com.anxys.erp.stockTransaction.domain.StockTransaction;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存流水更新表单
 * @since 2025-06-09 16:59:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockTransactionUpdateParam extends StockTransaction {

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;

}
