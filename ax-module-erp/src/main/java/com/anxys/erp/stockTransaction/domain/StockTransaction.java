package com.anxys.erp.stockTransaction.domain;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import com.anxys.framework.common.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;


/**
 * 库存流水主表实体类
 *
 * @since 2025-06-12 13:26:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("erp_stock_transaction")
public class StockTransaction extends BaseEntity {
    @Schema(description = "主键id")
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
    @Schema(description = "流水单号（唯一）")
    private String transactionNo;
    @Schema(description = "字典 | 流水类型 transaction_type：IN_PURCHASE-采购入库，IN_RETURN-退货入库，IN_TRANSFER-调拨入库，IN_ADJUST-调整入库，IN_PRODUCTION-生产入库，OUT_SALES-销售出库，OUT_RETURN-退货出库，OUT_TRANSFER-调拨出库，OUT_ADJUST-调整出库，OUT_PRODUCTION-生产出库")
    private String transactionType;
    @Schema(description = "流水日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate transactionDate;
    @Schema(description = "字典 | 关联单据类型 related_type：ORDER-订单，PURCHASE-采购单，TRANSFER-调拨单，ADJUST-调整单，CHECK-盘点单")
    private String relatedType;
    @Schema(description = "关联单据ID")
    private Long relatedId;
    @Schema(description = "关联单据号")
    private String relatedNo;
    @Schema(description = "操作人ID | t_employee表的employee_id")
    private Long operatorId;
    @Schema(description = "操作人姓名")
    private String operatorName;
    @Schema(description = "总数量（所有明细汇总）")
    private Double totalQuantity;
    @Schema(description = "总金额（所有明细汇总）")
    private Double totalAmount;
    @Schema(description = "明细条数")
    private Integer itemCount;
    @Schema(description = "字典 | 审核状态 audit_status：PENDING-待审核，APPROVED-已审核，REJECTED-已拒绝，DRAFT-草稿")
    private String auditStatus;
    @Schema(description = "计划处理数量")
    private Double plannedQuantity;
    @Schema(description = "已处理数量")
    private Double processedQuantity;
    @Schema(description = "剩余未处理数量")
    private Double remainingQuantity;
    @Schema(description = "字典 | 处理状态 process_status：PENDING-待处理，IN_PROGRESS-处理中，COMPLETED-已完成，CANCELLED-已取消")
    private String processStatus;
    @Schema(description = "流水备注")
    private String remark;
}
