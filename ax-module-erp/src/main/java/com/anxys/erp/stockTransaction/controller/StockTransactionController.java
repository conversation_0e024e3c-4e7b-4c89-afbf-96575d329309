package com.anxys.erp.stockTransaction.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.anxys.erp.stockTransaction.domain.excel.StockTransactionExcel;
import com.anxys.erp.stockTransaction.domain.form.*;
import com.anxys.erp.stockTransaction.domain.vo.StockTransactionVO;
import com.anxys.erp.stockTransaction.service.StockTransactionService;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.common.utils.DateFormatterEnum;
import com.anxys.framework.common.utils.ExcelUtil;
import com.anxys.framework.common.utils.LocalDateUtil;
import com.anxys.system.utils.AdminRequestUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存流水控制器
 *
 * @since 2025-06-09 16:59:21
 */
@Slf4j
@RestController
@RequestMapping("/stockTransaction")
@Tag(name = "库存流水接口")
public class StockTransactionController {

    @Resource
    private StockTransactionService stockTransactionService;

    @Operation(summary = "库存流水分页查询")
    @PostMapping("/stockTransactionPage")
    @SaCheckPermission("stock-transaction:query")
    public HttpResponse<PageResult<StockTransactionVO>> query(@RequestBody @Valid StockTransactionPageParam queryParam) {
        PageResult<StockTransactionVO> pageResult = stockTransactionService.page(queryParam);
        return HttpResponse.ok(pageResult);
    }

    @Operation(summary = "库存流水列表查询")
    @PostMapping("/stockTransactionList")
    @SaCheckPermission("stock-transaction:query")
    public HttpResponse<List<StockTransactionVO>> list(@RequestBody @Valid StockTransactionQueryParam queryParam) {
        List<StockTransactionVO> list = stockTransactionService.list(queryParam);
        return HttpResponse.ok(list);
    }

    @Operation(summary = "获取库存流水详情")
    @GetMapping("/stockTransactionDetail/{id}")
    @SaCheckPermission("stock-transaction:query")
    public HttpResponse<StockTransactionVO> detail(@PathVariable Long id) {
        StockTransactionVO stockTransactionVO = stockTransactionService.queryById(id);
        return HttpResponse.ok(stockTransactionVO);
    }

    @Operation(summary = "添加库存流水")
    @PostMapping("/addStockTransaction")
    @SaCheckPermission("stock-transaction:add")
    public HttpResponse<StockTransactionVO> insert(@RequestBody @Valid StockTransactionAddParam addParam) {
        StockTransactionVO result = stockTransactionService.insert(addParam);
        return HttpResponse.ok(result);
    }

    @Operation(summary = "更新库存流水")
    @PostMapping("/updateStockTransaction")
    @SaCheckPermission("stock-transaction:update")
    public HttpResponse<String> update(@RequestBody @Valid StockTransactionUpdateParam updateParam) {
        stockTransactionService.update(updateParam);
        return HttpResponse.ok();
    }

    @Operation(summary = "删除库存流水")
    @GetMapping("/deleteStockTransaction/{id}")
    @SaCheckPermission("stock-transaction:delete")
    public HttpResponse<String> delete(@PathVariable Long id) {
        stockTransactionService.delete(id);
        return HttpResponse.ok();
    }

    @Operation(summary = "批量删除库存流水")
    @PostMapping("/batchDeleteStockTransaction")
    @SaCheckPermission("stock-transaction:delete")
    public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
        stockTransactionService.batchDelete(idList);
        return HttpResponse.ok();
    }

    @Operation(summary = "导出库存流水Excel")
    @GetMapping("/exportStockTransactionExcel")
    @SaCheckPermission("stock-transaction:export")
    public void exportExcel(@Parameter(description = "查询参数") StockTransactionQueryParam queryParam, HttpServletResponse response) throws IOException {
        List<StockTransactionExcel> list = stockTransactionService.exportExcel(queryParam);
        // 添加水印
        String watermark = AdminRequestUtil.getRequestUser().getActualName();
        watermark += LocalDateUtil.format(LocalDateTime.now(), DateFormatterEnum.YMD_HMS);

        ExcelUtil.exportExcelWithWatermark(response, "库存流水列表.xlsx", "库存流水", StockTransactionExcel.class, list, watermark);
    }

    @Operation(summary = "导入库存流水Excel")
    @PostMapping("/importStockTransactionExcel")
    @SaCheckPermission("stock-transaction:import")
    public HttpResponse<String> importExcel(@RequestParam("file") MultipartFile file) {
        int count = stockTransactionService.importExcel(file);
        return HttpResponse.okMsg("成功导入" + count + "条数据");
    }

    @Operation(summary = "创建库存流水单据")
    @PostMapping("/createStockTransaction")
    @SaCheckPermission("stock-transaction:add")
    public HttpResponse<String> createTransaction(@RequestBody @Valid StockTransactionSaveParam saveParam) {
        String transactionNo = stockTransactionService.createTransaction(saveParam);
        return HttpResponse.ok(transactionNo);
    }

    @Operation(summary = "更新库存流水单据")
    @PostMapping("/updateStockTransactionDoc")
    @SaCheckPermission("stock-transaction:update")
    public HttpResponse<String> updateTransaction(@RequestBody @Valid StockTransactionSaveParam updateParam) {
        stockTransactionService.updateTransaction(updateParam);
        return HttpResponse.ok();
    }

    @Operation(summary = "库存流水审核")
    @PostMapping("/auditStockTransaction")
    @SaCheckPermission("stock-transaction:audit")
    public HttpResponse<String> auditTransaction(@RequestBody @Valid StockTransactionAuditParam auditParam) {
        stockTransactionService.updateAuditStatus(auditParam.getId(), auditParam.getAuditStatus());
        return HttpResponse.okMsg("审核完成");
    }
}
