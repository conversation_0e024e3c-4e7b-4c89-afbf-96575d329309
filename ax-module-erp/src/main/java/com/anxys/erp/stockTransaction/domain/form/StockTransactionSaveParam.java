package com.anxys.erp.stockTransaction.domain.form;

import com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemAddParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 库存流水保存参数")
@Data
public class StockTransactionSaveParam {

    @Schema(description = "库存流水主表参数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "库存流水主表参数不能为空")
    @Valid
    private StockTransactionAddParam stockTransaction;

    @Schema(description = "库存流水明细列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "库存流水明细列表不能为空")
    @Valid
    private List<StockTransactionItemAddParam> items;



}
