package com.anxys.erp.stockTransaction.domain.form;


import com.anxys.erp.stockTransaction.domain.StockTransaction;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 库存流水查询表单
 *
 * @since 2025-06-09 16:59:21
 */
@EqualsAndHashCode(callSuper = false)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class StockTransactionQueryParam extends StockTransaction {

            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "流水日期始")
            private LocalDate transactionDateFrom;

            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "流水日期至")
            private LocalDate transactionDateTo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "创建时间始")
            private LocalDateTime createTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "创建时间至")
            private LocalDateTime createTimeTo;
}
