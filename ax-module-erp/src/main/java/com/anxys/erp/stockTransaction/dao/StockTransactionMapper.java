package com.anxys.erp.stockTransaction.dao;


import com.anxys.erp.stockTransaction.domain.StockTransaction;
import com.anxys.erp.stockTransaction.domain.excel.StockTransactionExcel;
import com.anxys.erp.stockTransaction.domain.form.StockTransactionPageParam;
import com.anxys.erp.stockTransaction.domain.form.StockTransactionQueryParam;
import com.anxys.erp.stockTransaction.domain.vo.StockTransactionVO;
import com.anxys.framework.mybatis.mapper.BaseMapperX;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存流水数据库访问层
 * @since 2025-06-09 16:59:21
 *
 */
@Mapper
public interface StockTransactionMapper extends BaseMapperX<StockTransaction> {

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param queryParam 查询参数
     * @return 对象列表
     */
    List<StockTransactionVO> page(Page page, @Param("param") StockTransactionPageParam queryParam);


    /**
    * 查询列表
    * @param queryParam 查询参数
    * @return 对象列表
    *
    */
    List<StockTransactionVO> list(@Param("param") StockTransactionQueryParam queryParam);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList") List<Long> idList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 批量插入
     */
    void batchInsert(List<StockTransaction> entityList);


    /**
     * 根据ID删除
     *
     * @param id ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);


    /**
     * 导出Excel
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<StockTransactionExcel> exportExcel(@Param("query") StockTransactionQueryParam queryParam);

    StockTransaction selectByTransactionNo(@Param("transactionNo") String transactionNo);

}
