package com.anxys.erp.stockTransaction.domain.excel;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 库存流水Excel导出VO
 * @since 2025-06-09 16:59:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTransactionExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

@ExcelProperty("流水单号")        private String transactionNo;
@ExcelProperty("仓库ID | erp_warehouse表的id")        private Long warehouseId;
@ExcelProperty("仓库编码")        private String warehouseCode;
@ExcelProperty("仓库名称")        private String warehouseName;
@ExcelProperty("商品ID | erp_product表的id")        private Long productId;
@ExcelProperty("商品编码")        private String productCode;
@ExcelProperty("商品名称")        private String productName;
@ExcelProperty("SKU ID | erp_sku表的id")        private Long skuId;
@ExcelProperty("SKU编码")        private String skuCode;
@ExcelProperty("规格摘要")        private String specSummary;
@ExcelProperty("单位")        private String unit;
@ExcelProperty("字典 | 流水类型 transaction_type：IN_PURCHASE-采购入库，IN_RETURN-退货入库，IN_TRANSFER-调拨入库，IN_ADJUST-调整入库，IN_PRODUCTION-生产入库，OUT_SALES-销售出库，OUT_RETURN-退货出库，OUT_TRANSFER-调拨出库，OUT_ADJUST-调整出库，OUT_PRODUCTION-生产出库")        private String transactionType;
@ExcelProperty("流水日期")        private LocalDate transactionDate;
@ExcelProperty("变动数量 (正数入库，负数出库)")        private Double quantity;
@ExcelProperty("单位成本")        private Double unitCost;
@ExcelProperty("总金额")        private Double totalAmount;
@ExcelProperty("变动前库存")        private Double beforeStock;
@ExcelProperty("变动后库存")        private Double afterStock;
@ExcelProperty("字典 | 关联单据类型 related_type：ORDER-订单，PURCHASE-采购单，TRANSFER-调拨单，ADJUST-调整单，CHECK-盘点单")        private String relatedType;
@ExcelProperty("关联单据ID")        private Long relatedId;
@ExcelProperty("关联单据号")        private String relatedNo;
@ExcelProperty("操作人ID | t_employee表的employee_id")        private Long operatorId;
@ExcelProperty("操作人姓名")        private String operatorName;
@ExcelProperty("备注")        private String remark;
@ExcelProperty("创建人")        private Long createdBy;
@ExcelProperty("创建时间")        private LocalDateTime createTime;
}
