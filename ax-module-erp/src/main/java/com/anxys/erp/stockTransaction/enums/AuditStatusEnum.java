package com.anxys.erp.stockTransaction.enums;

/**
 * 审核状态枚举
 * 对应字典: audit_status
 * 用于验证库存流水审核状态的合法性
 *
 * <AUTHOR>
 */
public enum AuditStatusEnum {

    /**
     * 待审核
     */
    PENDING("PENDING", "待审核"),

    /**
     * 已审核
     */
    APPROVED("APPROVED", "已审核"),

    /**
     * 已拒绝
     */
    REJECTED("REJECTED", "已拒绝"),

    /**
     * 已作废（替换原来的DRAFT-草稿）
     */
    CANCELLED("CANCELLED", "已作废");

    private final String code;
    private final String description;

    AuditStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 枚举实例，如果不存在则返回null
     */
    public static AuditStatusEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (AuditStatusEnum status : AuditStatusEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 验证状态代码是否有效
     *
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return fromCode(code) != null;
    }

    /**
     * 获取所有有效状态代码
     *
     * @return 有效状态代码数组
     */
    public static String[] getAllCodes() {
        AuditStatusEnum[] values = AuditStatusEnum.values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有有效状态描述信息
     *
     * @return 状态描述信息字符串
     */
    public static String getValidStatusInfo() {
        StringBuilder sb = new StringBuilder();
        for (AuditStatusEnum status : AuditStatusEnum.values()) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append(status.getCode()).append("-").append(status.getDescription());
        }
        return sb.toString();
    }

    /**
     * 验证状态转换是否允许
     * 业务规则：
     * - 待审核 -> 已审核/已拒绝/已作废
     * - 已拒绝 -> 待审核/已作废
     * - 已审核 -> 已作废 (特殊情况)
     * - 已作废 -> 不允许转换
     *
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return 是否允许转换
     */
    public static boolean isTransitionAllowed(String fromStatus, String toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        // 相同状态不允许转换
        if (fromStatus.equals(toStatus)) {
            return false;
        }

        AuditStatusEnum from = fromCode(fromStatus);
        AuditStatusEnum to = fromCode(toStatus);

        if (from == null || to == null) {
            return false;
        }

        switch (from) {
            case PENDING:
                // 待审核可以转换为已审核、已拒绝、已作废
                return to == APPROVED || to == REJECTED || to == CANCELLED;
            case REJECTED:
                // 已拒绝可以转换为待审核、已作废
                return to == PENDING || to == CANCELLED;
            case APPROVED:
                // 已审核只能转换为已作废（特殊情况，如错误处理）
                return to == CANCELLED;
            case CANCELLED:
                // 已作废不允许转换到任何状态
                return false;
            default:
                return false;
        }
    }

    /**
     * 验证审核状态参数合法性
     *
     * @param auditStatus 审核状态
     * @throws IllegalArgumentException 如果状态不合法
     */
    public static void validateAuditStatus(String auditStatus) {
        if (auditStatus == null || auditStatus.trim().isEmpty()) {
            throw new IllegalArgumentException("审核状态不能为空");
        }

        if (!isValid(auditStatus)) {
            throw new IllegalArgumentException("无效的审核状态: " + auditStatus +
                ", 有效状态: " + getValidStatusInfo());
        }
    }

    /**
     * 验证状态转换合法性
     *
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @throws IllegalArgumentException 如果转换不合法
     */
    public static void validateStatusTransition(String fromStatus, String toStatus) {
        validateAuditStatus(fromStatus);
        validateAuditStatus(toStatus);

        if (fromStatus.equals(toStatus)) {
            AuditStatusEnum status = fromCode(toStatus);
            throw new IllegalArgumentException("当前状态已经是 " +
                (status != null ? status.getDescription() : toStatus));
        }

        if (!isTransitionAllowed(fromStatus, toStatus)) {
            AuditStatusEnum from = fromCode(fromStatus);
            AuditStatusEnum to = fromCode(toStatus);
            String fromDesc = from != null ? from.getDescription() : fromStatus;
            String toDesc = to != null ? to.getDescription() : toStatus;
            throw new IllegalArgumentException("不允许从 " + fromDesc + " 转换为 " + toDesc);
        }
    }
}
