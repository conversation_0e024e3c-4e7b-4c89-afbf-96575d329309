package com.anxys.erp.stockTransaction.service;

import cn.idev.excel.FastExcel;
import com.anxys.erp.order.salesOrder.service.ErpNoRedisDAO;
import com.anxys.erp.stock.service.StockService;
import com.anxys.erp.stockTransaction.dao.StockTransactionMapper;
import com.anxys.erp.stockTransaction.domain.StockTransaction;
import com.anxys.erp.stockTransaction.domain.excel.StockTransactionExcel;
import com.anxys.erp.stockTransaction.domain.form.*;
import com.anxys.erp.stockTransaction.domain.vo.StockTransactionVO;
import com.anxys.erp.stockTransaction.enums.AuditStatusEnum;
import com.anxys.erp.stockTransactionItem.dao.StockTransactionItemMapper;
import com.anxys.erp.stockTransactionItem.domain.StockTransactionItem;
import com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemAddParam;
import com.anxys.erp.warehouse.service.WarehouseService;
import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import com.anxys.framework.common.utils.object.BeanUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static com.anxys.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.anxys.framework.common.utils.collection.CollectionUtils.convertSet;

/**
 * 库存流水服务层
 *
 * @since 2025-06-09 16:59:22
 */
@Slf4j
@Service
public class StockTransactionService {

    @Resource
    private StockTransactionMapper stockTransactionMapper;

    @Resource
    private ErpNoRedisDAO erpNoRedisDAO;

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private StockTransactionItemMapper stockTransactionItemMapper;

    @Resource
    private StockService stockService;

    /**
     * 分页查询
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    public PageResult<StockTransactionVO> page(StockTransactionPageParam queryParam) {
        Page<?> page = PageUtil.convert2PageQuery(queryParam.getPageParam());
        List<StockTransactionVO> PageVo = stockTransactionMapper.page(page, queryParam);
        return PageUtil.convert2PageResult(page, PageVo);
    }

    /**
     * 列表查询
     *
     * @param queryParam 查询参数
     * @return 列表结果
     */
    public List<StockTransactionVO> list(StockTransactionQueryParam queryParam) {
        return stockTransactionMapper.list(queryParam);
    }

    /**
     * 添加
     *
     * @param addParam 添加参数
     * @return 新创建的对象VO
     */
    @Transactional(rollbackFor = Exception.class)
    public StockTransactionVO insert(StockTransactionAddParam addParam) {
        StockTransaction stockTransaction = BeanUtil.copy(addParam, StockTransaction.class);
        stockTransactionMapper.insert(stockTransaction);
        // 返回新创建的对象
        return BeanUtil.copy(stockTransaction, StockTransactionVO.class);
    }

    /**
     * 更新
     *
     * @param updateParam 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(StockTransactionUpdateParam updateParam) {
        StockTransaction stockTransaction = BeanUtil.copy(updateParam, StockTransaction.class);
        stockTransactionMapper.updateById(stockTransaction);
    }

    /**
     * 批量删除
     *
     * @param idList ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        if (!idList.isEmpty()) {
            stockTransactionMapper.deleteByIds(idList, true);
        }
    }

    /**
     * 单个删除
     *
     * @param id ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        stockTransactionMapper.deleteById(id);
    }

    /**
     * 通过ID查询
     *
     * @param id ID
     * @return 详情
     */
    public StockTransactionVO queryById(Long id) {
        StockTransaction stockTransaction = stockTransactionMapper.selectById(id);
        if (stockTransaction == null) {
            throw new BusinessException(UserErrorCode.DATA_NOT_EXIST);
        }
        return BeanUtil.copy(stockTransaction, StockTransactionVO.class);
    }

    /**
     * 导出Excel
     *
     * @param queryParam 查询参数
     * @return Excel数据列表
     */
    public List<StockTransactionExcel> exportExcel(StockTransactionQueryParam queryParam) {
        return stockTransactionMapper.exportExcel(queryParam);
    }


    /**
     * 导入Excel
     *
     * @param file Excel文件
     * @return 导入的数据条数
     */
    @Transactional(rollbackFor = Exception.class)
    public int importExcel(MultipartFile file) {
        List<StockTransactionExcel> dataList;
        try {
            dataList = FastExcel.read(file.getInputStream())
                    .head(StockTransactionExcel.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            log.error("Excel导入失败", e);
            throw new BusinessException("数据格式存在问题，无法读取");
        }

        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException("数据为空");
        }

        // 处理导入数据
        log.info("开始处理导入数据，共 {} 条", dataList.size());
        List<StockTransaction> entityList = dataList.stream()
                .map(excel -> {
                    log.info("处理数据: {}", excel);
                    return BeanUtil.copy(excel, StockTransaction.class);
                })
                .toList();

        stockTransactionMapper.batchInsert(entityList);

        return dataList.size();
    }

    /**
     * 库存流水新增
     *
     * @since 2025-06-11 15:26:15
     */
    @Transactional(rollbackFor = Exception.class)
    public String createTransaction(StockTransactionSaveParam saveParam) {
        // 1.1 校验库存流水项的有效性
        List<StockTransactionItemAddParam> stockItems = validateStockInItems(saveParam.getItems());

        // 1.2 生成流水单号（一个单子里的所有东西共用一个流水单号）
        String no = erpNoRedisDAO.generate(ErpNoRedisDAO.STOCK_IN_NO_PREFIX);
        if (stockTransactionMapper.selectByTransactionNo(no) != null) {
            throw exception("生成流水单号失败，请重新提交");
        }
        // 1.3 生成流水单,返回流水单主键id
//        Long transactionId = createMainTransaction(saveParam, no, stockItems);
        StockTransaction stockTransaction = new StockTransaction();
        BeanUtils.copyProperties(saveParam.getStockTransaction(), stockTransaction);
        stockTransaction.setTransactionNo(no); // 设置生成的流水单号

        calculateTotal(stockItems, stockTransaction);

        // 插入主表
        long transactionId =  stockTransactionMapper.insert(stockTransaction);

        // 2.1 批量创建库存流水记录
        stockItems.forEach(item -> {
            // 设置单子级别的公共信息
            item.setTransactionId(transactionId);
            item.setTransactionNo(no);
        });
        List<StockTransactionItem> entityList = stockItems.stream()
                .map(item -> BeanUtil.copy(item, StockTransactionItem.class))
                .toList();
        stockTransactionItemMapper.batchInsert(entityList);

        return no;
    }


    /**
     * 库存流水更新
     *
     * @since 2025-06-11 15:26:15
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTransaction(StockTransactionSaveParam updateParam) {

        if(updateParam.getStockTransaction().getId()==null){
            throw new BusinessException("流水单id不能为空");
        }
        // 1.1 校验库存流水项的有效性
        List<StockTransactionItemAddParam> stockItems = validateStockInItems(updateParam.getItems());


        // 2.2 重新创建流水记录
        StockTransaction stockTransaction = new StockTransactionUpdateParam();
        BeanUtils.copyProperties(updateParam.getStockTransaction(), stockTransaction);
        calculateTotal(stockItems, stockTransaction);

        // 2.3 更新主表
        stockTransactionMapper.updateById(stockTransaction);

        // 2.4 更新具体流水
        stockItems.forEach(item -> {
            stockTransactionItemMapper.updateById(item);
        });


    }

    /**
     * 库存流水审核状态更新
     *
     * @param id 库存流水ID
     * @param newStatus 新的审核状态
     * @since 2025-06-11 15:26:15
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAuditStatus(Long id, String newStatus) {
        // 1. 使用枚举验证审核状态合法性
        try {
            AuditStatusEnum.validateAuditStatus(newStatus);
        } catch (IllegalArgumentException e) {
            throw exception(e.getMessage());
        }

        // 2. 校验库存流水记录存在
        StockTransaction stockTransaction = validateStockExists(id);
        String currentStatus = stockTransaction.getAuditStatus();

        // 3. 使用枚举验证状态转换合法性
        try {
            AuditStatusEnum.validateStatusTransition(currentStatus, newStatus);
        } catch (IllegalArgumentException e) {
            throw exception(e.getMessage());
        }

        // 4. 更新状态
        stockTransaction.setAuditStatus(newStatus);
        int updateCount = stockTransactionMapper.updateById(stockTransaction);
        if (updateCount == 0) {
            throw exception("审核失败");
        }

        // 5. 记录日志
        log.info("库存流水[{}]审核状态从[{}]更新为[{}]",
                stockTransaction.getTransactionNo(), currentStatus, newStatus);

        // 6. 根据业务需求实现库存变更逻辑
        if ("APPROVED".equals(newStatus)) {
            // 审核通过时，更新实际库存
            updateStockByTransaction(stockTransaction);
        }
    }

    // 统计明细总数量、总金额、明细条数
    private static void calculateTotal(List<StockTransactionItemAddParam> stockItems, StockTransaction stockTransaction) {
        double totalQuantity = stockItems.stream()
                .mapToDouble(item -> item.getQuantity() != null ? item.getQuantity() : 0.0)
                .sum();
        double totalAmount = stockItems.stream()
                .mapToDouble(item -> item.getTotalAmount() != null ? item.getTotalAmount() : 0.0)
                .sum();
        int itemCount = stockItems.size();

        stockTransaction.setTotalQuantity(totalQuantity);
        stockTransaction.setTotalAmount(totalAmount);
        stockTransaction.setItemCount(itemCount);
    }


    private List<StockTransactionItemAddParam> validateStockInItems(List<StockTransactionItemAddParam> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("库存流水项列表不能为空");
        }

        // 1.2 校验仓库存在
        warehouseService.validWarehouseList(convertSet(
                list, StockTransactionItemAddParam::getWarehouseId));

        return list;
    }


    private StockTransaction validateStockExists(Long id) {
        StockTransaction stockTransaction = stockTransactionMapper.selectById(id);
        if (stockTransaction == null) {
            throw exception("流水单不存在");
        }
        return stockTransaction;
    }

    /**
     * 根据库存流水更新实际库存
     *
     * @param stockTransaction 库存流水主表信息
     */
    private void updateStockByTransaction(StockTransaction stockTransaction) {
        // 查询该流水单的所有明细
        List<StockTransactionItem> items = stockTransactionItemMapper.selectByTransactionId(stockTransaction.getId());

        if (CollectionUtils.isEmpty(items)) {
            log.warn("流水单[{}]没有找到明细数据", stockTransaction.getTransactionNo());
            return;
        }

        // 遍历明细，更新库存
        for (StockTransactionItem item : items) {
            try {
                // 调用库存服务更新库存（quantity字段正数表示入库，负数表示出库）
                Double afterStock = stockService.updateStockCountIncrement(
                        item.getSkuId(),
                        item.getWarehouseId(),
                        item.getQuantity(),
                        item

                );

                // 更新明细表中的变动后库存字段
                item.setAfterStock(afterStock);
                item.setBeforeStock(afterStock - item.getQuantity());
                stockTransactionItemMapper.updateById(item);

                log.info("更新库存成功 - SKU[{}] 仓库[{}] 变动数量[{}] 变动后库存[{}]",
                        item.getSkuId(), item.getWarehouseId(), item.getQuantity(), afterStock);

            } catch (Exception e) {
                log.error("更新库存失败 - SKU[{}] 仓库[{}] 变动数量[{}], 错误信息: {}",
                        item.getSkuId(), item.getWarehouseId(), item.getQuantity(), e.getMessage());
                throw new BusinessException("库存更新失败: " + e.getMessage());
            }
        }

        log.info("流水单[{}]库存更新完成，共处理{}条明细",
                stockTransaction.getTransactionNo(), items.size());
    }



}
