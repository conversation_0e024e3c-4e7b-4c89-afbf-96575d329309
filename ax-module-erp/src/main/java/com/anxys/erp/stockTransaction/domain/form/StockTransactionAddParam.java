package com.anxys.erp.stockTransaction.domain.form;

import com.anxys.erp.stockTransaction.domain.StockTransaction;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 库存流水主表添加表单
 *
 * @since 2025-06-11 15:26:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockTransactionAddParam extends StockTransaction {


    private String transactionNo;
    @NotBlank(message = "字典 | 流水类型：IN_PURCHASE-采购入库，IN_RETURN-退货入库，IN_TRANSFER-调拨入库，IN_ADJUST-调整入库，IN_PRODUCTION-生产入库，OUT_SALES-销售出库，OUT_RETURN-退货出库，OUT_TRANSFER-调拨出库，OUT_ADJUST-调整出库，OUT_PRODUCTION-生产出库不能为空")
    private String transactionType;
    @NotNull(message = "流水日期不能为空")
    private LocalDate transactionDate;
}
