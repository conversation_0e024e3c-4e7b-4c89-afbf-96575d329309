package com.anxys.erp.stockTransaction.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 库存流水审核参数
 *
 * @since 2025-06-11 17:35:00
 */
@Data
@Schema(description = "库存流水审核参数")
public class StockTransactionAuditParam {

    @Schema(description = "库存流水ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "库存流水ID不能为空")
    private Long id;

    @Schema(description = "审核状态：PENDING-待审核，APPROVED-已审核，REJECTED-已拒绝，CANCELLED-已作废",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "审核状态不能为空")
    private String auditStatus;

    @Schema(description = "审核备注")
    private String auditRemark;
}
