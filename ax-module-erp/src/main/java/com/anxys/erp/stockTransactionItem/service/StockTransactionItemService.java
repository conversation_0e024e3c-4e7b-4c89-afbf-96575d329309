package com.anxys.erp.stockTransactionItem.service;

import cn.idev.excel.FastExcel;
import com.anxys.erp.stockTransactionItem.dao.StockTransactionItemMapper;
import com.anxys.erp.stockTransactionItem.domain.StockTransactionItem;
import com.anxys.erp.stockTransactionItem.domain.excel.StockTransactionItemExcel;
import com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemAddParam;
import com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemPageParam;
import com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemQueryParam;
import com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemUpdateParam;
import com.anxys.erp.stockTransactionItem.domain.vo.StockTransactionItemVO;
import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 库存流水明细表服务层
 *
 * @since 2025-06-11 15:07:26
 */
@Slf4j
@Service
@AllArgsConstructor
public class StockTransactionItemService {

    private StockTransactionItemMapper stockTransactionItemMapper;

    /**
     * 分页查询
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    public PageResult<StockTransactionItemVO> page(StockTransactionItemPageParam queryParam) {
        Page<?> page = PageUtil.convert2PageQuery(queryParam.getPageParam());
        List<StockTransactionItemVO> PageVo = stockTransactionItemMapper.page(page, queryParam);
        return PageUtil.convert2PageResult(page, PageVo);
    }

    /**
      * 列表查询
      *
      * @param queryParam 查询参数
      * @return 列表结果
      */
    public List<StockTransactionItemVO> list(StockTransactionItemQueryParam queryParam) {
        return stockTransactionItemMapper.list(queryParam);
    }

    /**
     * 添加
     *
     * @param addParam 添加参数
     * @return 新创建的对象VO
     */
    @Transactional(rollbackFor = Exception.class)
    public StockTransactionItemVO insert(StockTransactionItemAddParam addParam) {
        StockTransactionItem stockTransactionItem = BeanUtil.copy(addParam, StockTransactionItem. class);
            stockTransactionItemMapper.insert(stockTransactionItem);
        // 返回新创建的对象
        return BeanUtil.copy(stockTransactionItem, StockTransactionItemVO.class);
    }

    /**
     * 更新
     *
     * @param updateParam 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(StockTransactionItemUpdateParam updateParam) {
        StockTransactionItem stockTransactionItem =BeanUtil.copy(updateParam, StockTransactionItem. class);
            stockTransactionItemMapper.updateById(stockTransactionItem);
    }

    /**
     * 批量删除
     *
     * @param idList ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        if (!idList.isEmpty()) {
                stockTransactionItemMapper.deleteByIds(idList, true);
        }
    }

    /**
     * 单个删除
     *
     * @param id ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
            stockTransactionItemMapper.deleteById(id);
    }

    /**
     * 通过ID查询
     *
     * @param id ID
     * @return 详情
     */
    public StockTransactionItemVO queryById(Long id) {
        StockTransactionItem stockTransactionItem =stockTransactionItemMapper.selectById(id);
        if (stockTransactionItem ==null){
            throw new BusinessException(UserErrorCode.DATA_NOT_EXIST);
        }
        return BeanUtil.copy(stockTransactionItem, StockTransactionItemVO.class);
    }

    /**
     * 导出Excel
     *
     * @param queryParam 查询参数
     * @return Excel数据列表
     */
    public List<StockTransactionItemExcel> exportExcel(StockTransactionItemQueryParam queryParam) {
        return stockTransactionItemMapper.exportExcel(queryParam);
    }


    /**
     * 导入Excel
     *
     * @param file Excel文件
     * @return 导入的数据条数
     */
    @Transactional(rollbackFor = Exception.class)
    public int importExcel(MultipartFile file) {
        List<StockTransactionItemExcel> dataList;
        try {
            dataList = FastExcel.read(file.getInputStream())
                    .head(StockTransactionItemExcel.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            log.error("Excel导入失败", e);
            throw new BusinessException("数据格式存在问题，无法读取");
        }

        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException("数据为空");
        }

        // 处理导入数据
        log.info("开始处理导入数据，共 {} 条", dataList.size());
        List<StockTransactionItem> entityList = dataList.stream()
                .map(excel -> {
                    log.info("处理数据: {}", excel);
                    return BeanUtil.copy(excel, StockTransactionItem.class);
                })
                .toList();

            stockTransactionItemMapper.batchInsert(entityList);

        return dataList.size();
    }
}
