package com.anxys.erp.stockTransactionItem.domain.form;

import com.anxys.erp.stockTransactionItem.domain.StockTransactionItem;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 库存流水明细表查询表单
 *
 * @since 2025-06-11 15:07:25
 */
@EqualsAndHashCode(callSuper = false)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class StockTransactionItemQueryParam extends StockTransactionItem {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间始")
    private LocalDateTime createTimeFrom;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间至")
    private LocalDateTime createTimeTo;
}
