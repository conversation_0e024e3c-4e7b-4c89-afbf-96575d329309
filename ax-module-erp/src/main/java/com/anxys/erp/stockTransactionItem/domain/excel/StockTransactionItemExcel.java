package com.anxys.erp.stockTransactionItem.domain.excel;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 库存流水明细表Excel导出VO
 * @since 2025-06-11 15:07:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTransactionItemExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

@ExcelProperty("流水主表ID（外键）")        private Long transactionId;
@ExcelProperty("流水单号（冗余字段）")        private String transactionNo;
@ExcelProperty("仓库ID | erp_warehouse表的id")        private Long warehouseId;
@ExcelProperty("仓库编码")        private String warehouseCode;
@ExcelProperty("仓库名称")        private String warehouseName;
@ExcelProperty("商品ID | erp_product表的id")        private Long productId;
@ExcelProperty("商品编码")        private String productCode;
@ExcelProperty("商品名称")        private String productName;
@ExcelProperty("SKU ID | erp_sku表的id")        private Long skuId;
@ExcelProperty("SKU编码")        private String skuCode;
@ExcelProperty("规格摘要")        private String specSummary;
@ExcelProperty("单位")        private String unit;
@ExcelProperty("变动数量 (正数入库，负数出库)")        private Double quantity;
@ExcelProperty("单位成本")        private Double unitCost;
@ExcelProperty("总金额")        private Double totalAmount;
@ExcelProperty("变动前库存")        private Double beforeStock;
@ExcelProperty("变动后库存")        private Double afterStock;
@ExcelProperty("明细备注")        private String remark;
@ExcelProperty("创建人")        private Long createdBy;
@ExcelProperty("创建时间")        private LocalDateTime createTime;
}
