package com.anxys.erp.stockTransactionItem.domain.form;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.anxys.erp.stockTransactionItem.domain.StockTransactionItem;

/**
 * 库存流水明细表添加表单
 *
 * @since 2025-06-11 15:07:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockTransactionItemAddParam extends StockTransactionItem {

//    @NotNull(message = "流水主表ID（外键）不能为空")
//    private Long transactionId;
//    @NotBlank(message = "流水单号（冗余字段）不能为空")
//    private String transactionNo;
    @NotNull(message = "仓库ID | erp_warehouse表的id不能为空")
    private Long warehouseId;
    @NotBlank(message = "仓库名称不能为空")
    private String warehouseName;
    @NotNull(message = "商品ID | erp_product表的id不能为空")
    private Long productId;
    @NotBlank(message = "商品名称不能为空")
    private String productName;
    @NotNull(message = "SKU ID | erp_sku表的id不能为空")
    private Long skuId;
    @NotNull(message = "变动数量 (正数入库，负数出库)不能为空")
    private Double quantity;
    @NotBlank(message = "单位不能为空")
    private String unit;
    @NotNull(message = "单位成本不能为空")
    private Double unitCost;
    @NotNull(message = "总金额不能为空")
    private Double totalAmount;

}
