package com.anxys.erp.stockTransactionItem.domain.form;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.anxys.erp.stockTransactionItem.domain.StockTransactionItem;

/**
 * 库存流水明细表更新表单
 * @since 2025-06-11 15:07:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockTransactionItemUpdateParam extends StockTransactionItem {

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;

}
