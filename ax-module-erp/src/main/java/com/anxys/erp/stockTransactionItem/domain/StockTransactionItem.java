package com.anxys.erp.stockTransactionItem.domain;

import com.anxys.framework.common.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * 库存流水明细表实体类
 *
 * @since 2025-06-11 15:07:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("erp_stock_transaction_item")
public class StockTransactionItem extends BaseEntity {
    @Schema(description = "主键id")
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
            @Schema(description = "流水主表ID（外键）")
            private Long transactionId;
            @Schema(description = "流水单号（冗余字段）")
            private String transactionNo;
            @Schema(description = "仓库ID | erp_warehouse表的id")
            private Long warehouseId;
            @Schema(description = "仓库编码")
            private String warehouseCode;
            @Schema(description = "仓库名称")
            private String warehouseName;
            @Schema(description = "商品ID | erp_product表的id")
            private Long productId;
            @Schema(description = "商品编码")
            private String productCode;
            @Schema(description = "商品名称")
            private String productName;
            @Schema(description = "SKU ID | erp_sku表的id")
            private Long skuId;
            @Schema(description = "SKU编码")
            private String skuCode;
            @Schema(description = "规格摘要")
            private String specSummary;
            @Schema(description = "单位")
            private String unit;
            @Schema(description = "变动数量 (正数入库，负数出库)")
            private Double quantity;
            @Schema(description = "单位成本")
            private Double unitCost;
            @Schema(description = "总金额")
            private Double totalAmount;
            @Schema(description = "变动前库存")
            private Double beforeStock;
            @Schema(description = "变动后库存")
            private Double afterStock;
            @Schema(description = "明细备注")
            private String remark;
}
