package com.anxys.erp.stockTransactionItem.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.common.utils.DateFormatterEnum;
import com.anxys.framework.common.utils.ExcelUtil;
import com.anxys.framework.common.utils.LocalDateUtil;
import com.anxys.system.utils.AdminRequestUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import  com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemAddParam;
import  com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemPageParam;
import  com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemQueryParam;
import  com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemUpdateParam;
import  com.anxys.erp.stockTransactionItem.domain.vo.StockTransactionItemVO;
import  com.anxys.erp.stockTransactionItem.domain.excel.StockTransactionItemExcel;
import com.anxys.erp.stockTransactionItem.service.StockTransactionItemService;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.Parameter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存流水明细表控制器
 *
 * @since 2025-06-11 15:07:25
 */
@Slf4j
@RestController
@RequestMapping("/stockTransactionItem")
@Tag(name = "库存流水明细表接口")
public class StockTransactionItemController {

    @Resource
    private StockTransactionItemService stockTransactionItemService;

    @Operation(summary = "库存流水明细表分页查询")
    @PostMapping("/stockTransactionItemPage")
    @SaCheckPermission("stock-transaction-item:query")
    public HttpResponse<PageResult<StockTransactionItemVO>> query(@RequestBody @Valid StockTransactionItemPageParam queryParam) {
        PageResult<StockTransactionItemVO> pageResult = stockTransactionItemService.page(queryParam);
        return HttpResponse.ok(pageResult);
    }

    @Operation(summary = "库存流水明细表列表查询")
    @PostMapping("/stockTransactionItemList")
    @SaCheckPermission("stock-transaction-item:query")
    public HttpResponse<List<StockTransactionItemVO>> list(@RequestBody @Valid StockTransactionItemQueryParam queryParam) {
        List<StockTransactionItemVO> list = stockTransactionItemService.list(queryParam);
        return HttpResponse.ok(list);
    }

    @Operation(summary = "获取库存流水明细表详情")
    @GetMapping("/stockTransactionItemDetail/{id}")
    @SaCheckPermission("stock-transaction-item:query")
    public HttpResponse<StockTransactionItemVO> detail(@PathVariable Long id) {
        StockTransactionItemVO stockTransactionItemVO = stockTransactionItemService.queryById(id);
        return HttpResponse.ok(stockTransactionItemVO);
    }

    @Operation(summary = "添加库存流水明细表")
    @PostMapping("/addStockTransactionItem")
    @SaCheckPermission("stock-transaction-item:add")
    public HttpResponse<StockTransactionItemVO> insert(@RequestBody @Valid StockTransactionItemAddParam addParam) {
        StockTransactionItemVO result = stockTransactionItemService.insert(addParam);
        return HttpResponse.ok(result);
    }

    @Operation(summary = "更新库存流水明细表")
    @PostMapping("/updateStockTransactionItem")
    @SaCheckPermission("stock-transaction-item:update")
    public HttpResponse<String> update(@RequestBody @Valid StockTransactionItemUpdateParam updateParam) {
        stockTransactionItemService.update(updateParam);
        return HttpResponse.ok();
    }

    @Operation(summary = "删除库存流水明细表")
    @GetMapping("/deleteStockTransactionItem/{id}")
    @SaCheckPermission("stock-transaction-item:delete")
    public HttpResponse<String> delete(@PathVariable Long id) {
        stockTransactionItemService.delete(id);
        return HttpResponse.ok();
    }

    @Operation(summary = "批量删除库存流水明细表")
    @PostMapping("/batchDeleteStockTransactionItem")
    @SaCheckPermission("stock-transaction-item:delete")
    public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
        stockTransactionItemService.batchDelete(idList);
        return HttpResponse.ok();
    }

    @Operation(summary = "导出库存流水明细表Excel")
    @GetMapping("/exportStockTransactionItemExcel")
    @SaCheckPermission("stock-transaction-item:export")
    public void exportExcel(@Parameter(description = "查询参数") StockTransactionItemQueryParam queryParam, HttpServletResponse response) throws IOException {
        List<StockTransactionItemExcel> list = stockTransactionItemService.exportExcel(queryParam);
        // 添加水印
        String watermark = AdminRequestUtil.getRequestUser().getActualName();
        watermark += LocalDateUtil.format(LocalDateTime.now(), DateFormatterEnum.YMD_HMS);

        ExcelUtil.exportExcelWithWatermark(response, "库存流水明细表列表.xlsx", "库存流水明细表", StockTransactionItemExcel.class, list, watermark);
    }

    @Operation(summary = "导入库存流水明细表Excel")
    @PostMapping("/importStockTransactionItemExcel")
    @SaCheckPermission("stock-transaction-item:import")
    public HttpResponse<String> importExcel(@RequestParam("file") MultipartFile file) {
        int count = stockTransactionItemService.importExcel(file);
        return HttpResponse.okMsg("成功导入" + count + "条数据");
    }
}
