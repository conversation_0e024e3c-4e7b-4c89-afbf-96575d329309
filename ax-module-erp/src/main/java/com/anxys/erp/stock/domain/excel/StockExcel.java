package com.anxys.erp.stock.domain.excel;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 库存信息Excel导出VO
 * @since 2025-06-09 16:58:32
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

@ExcelProperty("仓库ID | erp_warehouse表的id")        private Long warehouseId;
@ExcelProperty("仓库编码")        private String warehouseCode;
@ExcelProperty("仓库名称")        private String warehouseName;
@ExcelProperty("商品ID | erp_product表的id")        private Long productId;
@ExcelProperty("商品编码")        private String productCode;
@ExcelProperty("商品名称")        private String productName;
@ExcelProperty("SKU ID | erp_sku表的id")        private Long skuId;
@ExcelProperty("SKU编码")        private String skuCode;
@ExcelProperty("规格摘要")        private String specSummary;
@ExcelProperty("单位")        private String unit;
@ExcelProperty("当前库存")        private Double currentStock;
@ExcelProperty("可用库存 (当前库存-预留库存)")        private Double availableStock;
@ExcelProperty("预留库存 (已下单未发货)")        private Double reservedStock;
@ExcelProperty("在途库存 (已采购未入库)")        private Double inTransitStock;
@ExcelProperty("最小库存 (安全库存)")        private Double minStock;
@ExcelProperty("最大库存")        private Double maxStock;
@ExcelProperty("再订货点")        private Double reorderPoint;
@ExcelProperty("再订货量")        private Double reorderQuantity;
@ExcelProperty("平均成本")        private Double avgCost;
@ExcelProperty("最新成本")        private Double lastCost;
@ExcelProperty("库存总价值 (当前库存×平均成本)")        private Double totalValue;
@ExcelProperty("字典 | 库存状态 stock_status：NORMAL-正常，LOW_STOCK-库存不足，OUT_OF_STOCK-缺货，OVERSTOCKED-库存过多，FROZEN-冻结")        private String stockStatus;
@ExcelProperty("冻结数量")        private Double freezeQuantity;
@ExcelProperty("最后入库日期")        private LocalDate lastInDate;
@ExcelProperty("最后出库日期")        private LocalDate lastOutDate;
@ExcelProperty("最后盘点日期")        private LocalDate lastCheckDate;
@ExcelProperty("创建人")        private Long createdBy;
@ExcelProperty("更新人")        private Long updatedBy;
@ExcelProperty("创建时间")        private LocalDateTime createTime;
@ExcelProperty("更新时间")        private LocalDateTime updateTime;
}
