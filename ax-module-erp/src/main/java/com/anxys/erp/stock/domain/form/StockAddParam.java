package com.anxys.erp.stock.domain.form;


import com.anxys.erp.stock.domain.Stock;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 库存信息添加表单
 *
 * @since 2025-06-09 16:58:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class StockAddParam extends Stock {

    @NotNull(message = "仓库ID | erp_warehouse表的id不能为空")
    private Long warehouseId;
    @NotBlank(message = "仓库名称不能为空")
    private String warehouseName;
    @NotNull(message = "商品ID | erp_product表的id不能为空")
    private Long productId;
    @NotBlank(message = "商品名称不能为空")
    private String productName;
    @NotNull(message = "SKU ID | erp_sku表的id不能为空")
    private Long skuId;
    @NotNull(message = "当前库存不能为空")
    private Double currentStock;
    @NotNull(message = "可用库存 (当前库存-预留库存)不能为空")
    private Double availableStock;
    @NotNull(message = "预留库存 (已下单未发货)不能为空")
    private Double reservedStock;
    @NotNull(message = "在途库存 (已采购未入库)不能为空")
    private Double inTransitStock;
}
