package com.anxys.erp.stock.dao;


import com.anxys.erp.stock.domain.Stock;
import com.anxys.erp.stock.domain.excel.StockExcel;
import com.anxys.erp.stock.domain.form.StockPageParam;
import com.anxys.erp.stock.domain.form.StockQueryParam;
import com.anxys.erp.stock.domain.vo.StockVO;
import com.anxys.framework.mybatis.mapper.BaseMapperX;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存信息数据库访问层
 * @since 2025-06-09 16:58:32
 *
 */
@Mapper
public interface StockMapper extends BaseMapperX<Stock> {

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param queryParam 查询参数
     * @return 对象列表
     */
    List<StockVO> page(Page page, @Param("param") StockPageParam queryParam);


    /**
    * 查询列表
    * @param queryParam 查询参数
    * @return 对象列表
    *
    */
    List<StockVO> list(@Param("param") StockQueryParam queryParam);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList") List<Long> idList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 批量插入
     */
    void batchInsert(List<Stock> entityList);

    /**
     * 根据SKU和仓库查询库存
     *
     * @param skuId 商品SKU ID
     * @param warehouseId 仓库ID
     * @return 库存对象
     */
    Stock selectBySkuAndWarehouse(@Param("skuId") Long skuId, @Param("warehouseId") Long warehouseId);





    /**
     * 导出Excel
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<StockExcel> exportExcel(@Param("query") StockQueryParam queryParam);
}
