package com.anxys.erp.stock.domain;


import com.anxys.framework.common.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;


/**
 * 库存信息实体类
 *
 * @since 2025-06-09 16:58:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("erp_stock")
public class Stock extends BaseEntity {
    @Schema(description = "主键id")
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
            @Schema(description = "仓库ID | erp_warehouse表的id")
            private Long warehouseId;
            @Schema(description = "仓库编码")
            private String warehouseCode;
            @Schema(description = "仓库名称")
            private String warehouseName;
            @Schema(description = "商品ID | erp_product表的id")
            private Long productId;
            @Schema(description = "商品编码")
            private String productCode;
            @Schema(description = "商品名称")
            private String productName;
            @Schema(description = "SKU ID | erp_sku表的id")
            private Long skuId;
            @Schema(description = "SKU编码")
            private String skuCode;
            @Schema(description = "规格摘要")
            private String specSummary;
            @Schema(description = "单位")
            private String unit;
            @Schema(description = "当前库存")
            private Double currentStock;
            @Schema(description = "可用库存 (当前库存-预留库存)")
            private Double availableStock;
            @Schema(description = "预留库存 (已下单未发货)")
            private Double reservedStock;
            @Schema(description = "在途库存 (已采购未入库)")
            private Double inTransitStock;
            @Schema(description = "最小库存 (安全库存)")
            private Double minStock;
            @Schema(description = "最大库存")
            private Double maxStock;
            @Schema(description = "再订货点")
            private Double reorderPoint;
            @Schema(description = "再订货量")
            private Double reorderQuantity;
            @Schema(description = "平均成本")
            private Double avgCost;
            @Schema(description = "最新成本")
            private Double lastCost;
            @Schema(description = "库存总价值 (当前库存×平均成本)")
            private Double totalValue;
            @Schema(description = "字典 | 库存状态 stock_status：NORMAL-正常，LOW_STOCK-库存不足，OUT_OF_STOCK-缺货，OVERSTOCKED-库存过多，FROZEN-冻结")
            private String stockStatus;
            @Schema(description = "冻结数量")
            private Double freezeQuantity;
            @Schema(description = "最后入库日期")
                @JsonFormat(pattern = "yyyy-MM-dd")
            private LocalDate lastInDate;
            @Schema(description = "最后出库日期")
                @JsonFormat(pattern = "yyyy-MM-dd")
            private LocalDate lastOutDate;
            @Schema(description = "最后盘点日期")
                @JsonFormat(pattern = "yyyy-MM-dd")
            private LocalDate lastCheckDate;
}
