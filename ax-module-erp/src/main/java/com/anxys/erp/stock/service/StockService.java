package com.anxys.erp.stock.service;

import cn.idev.excel.FastExcel;
import com.anxys.erp.stock.dao.StockMapper;
import com.anxys.erp.stock.domain.Stock;
import com.anxys.erp.stock.domain.excel.StockExcel;
import com.anxys.erp.stock.domain.form.StockAddParam;
import com.anxys.erp.stock.domain.form.StockPageParam;
import com.anxys.erp.stock.domain.form.StockQueryParam;
import com.anxys.erp.stock.domain.form.StockUpdateParam;
import com.anxys.erp.stock.domain.vo.StockVO;
import com.anxys.erp.stockTransactionItem.domain.StockTransactionItem;
import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static com.anxys.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 库存信息服务层
 *
 * @since 2025-06-09 16:58:32
 */
@Slf4j
@Service
public class StockService {

    private static final Boolean NEGATIVE_STOCK_COUNT_ENABLE = false;


    @Resource
    private StockMapper stockMapper;

    /**
     * 分页查询
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    public PageResult<StockVO> page(StockPageParam queryParam) {
        Page<?> page = PageUtil.convert2PageQuery(queryParam.getPageParam());
        List<StockVO> PageVo = stockMapper.page(page, queryParam);
        return PageUtil.convert2PageResult(page, PageVo);
    }

    /**
     * 列表查询
     *
     * @param queryParam 查询参数
     * @return 列表结果
     */
    public List<StockVO> list(StockQueryParam queryParam) {
        return stockMapper.list(queryParam);
    }

    /**
     * 添加
     *
     * @param addParam 添加参数
     * @return 新创建的对象VO
     */
    @Transactional(rollbackFor = Exception.class)
    public StockVO insert(StockAddParam addParam) {
        Stock stock = BeanUtil.copy(addParam, Stock.class);
        stockMapper.insert(stock);
        // 返回新创建的对象
        return BeanUtil.copy(stock, StockVO.class);
    }

    /**
     * 更新
     *
     * @param updateParam 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(StockUpdateParam updateParam) {
        Stock stock = BeanUtil.copy(updateParam, Stock.class);
        stockMapper.updateById(stock);
    }

    /**
     * 批量删除
     *
     * @param idList ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        if (!idList.isEmpty()) {
            stockMapper.deleteByIds(idList, true);
        }
    }

    /**
     * 单个删除
     *
     * @param id ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        stockMapper.deleteById(id);
    }

    /**
     * 通过ID查询
     *
     * @param id ID
     * @return 详情
     */
    public StockVO queryById(Long id) {
        Stock stock = stockMapper.selectById(id);
        if (stock == null) {
            throw new BusinessException(UserErrorCode.DATA_NOT_EXIST);
        }
        return BeanUtil.copy(stock, StockVO.class);
    }

    /**
     * 导出Excel
     *
     * @param queryParam 查询参数
     * @return Excel数据列表
     */
    public List<StockExcel> exportExcel(StockQueryParam queryParam) {
        return stockMapper.exportExcel(queryParam);
    }


    /**
     * 导入Excel
     *
     * @param file Excel文件
     * @return 导入的数据条数
     */
    @Transactional(rollbackFor = Exception.class)
    public int importExcel(MultipartFile file) {
        List<StockExcel> dataList;
        try {
            dataList = FastExcel.read(file.getInputStream())
                    .head(StockExcel.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            log.error("Excel导入失败", e);
            throw new BusinessException("数据格式存在问题，无法读取");
        }

        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException("数据为空");
        }

        // 处理导入数据
        log.info("开始处理导入数据，共 {} 条", dataList.size());
        List<Stock> entityList = dataList.stream()
                .map(excel -> {
                    log.info("处理数据: {}", excel);
                    return BeanUtil.copy(excel, Stock.class);
                })
                .toList();

        stockMapper.batchInsert(entityList);

        return dataList.size();
    }

    /**
     * 更新库存数量（增量方式）
     *
     * @param skuId       商品SKU ID
     * @param warehouseId 仓库ID
     * @param count       变动数量（正数入库，负数出库）
     * @return 变动后的库存数量
     */
    @Transactional(rollbackFor = Exception.class)
    public Double updateStockCountIncrement(Long skuId, Long warehouseId, Double count, StockTransactionItem item) {
        // 根据SKU ID和仓库ID查询当前库存
        Stock stock = stockMapper.selectBySkuAndWarehouse(skuId, warehouseId);

        if (stock == null) {


            stock = BeanUtil.copy(item, Stock.class);

            // 清除从item复制过来的id，因为要创建新记录
            stock.setId(null);// 排除id字段，因为要创建新记录

            // 设置库存特有的字段
            stock.setCurrentStock(0.0);
            stock.setAvailableStock(0.0);
            stock.setReservedStock(0.0);
            stock.setInTransitStock(0.0);
            stock.setMinStock(0.0);
            stock.setMaxStock(0.0);
            stock.setReorderPoint(0.0);
            stock.setReorderQuantity(0.0);
            stock.setAvgCost(0.0);
            stock.setLastCost(0.0);
            stock.setTotalValue(0.0);
            stock.setStockStatus("NORMAL");
            stock.setFreezeQuantity(0.0);


            stockMapper.insert(stock);
        }

        // 计算变动后库存
        Double newStock = stock.getCurrentStock() + count;

        // 校验库存充足性（如果是出库操作）
        if (!NEGATIVE_STOCK_COUNT_ENABLE && newStock < 0) {
            throw exception("操作失败，SKU[" + skuId + "]在仓库[" + warehouseId + "]的库存不足，当前库存:" + stock.getCurrentStock() + "，需要变动:" + count);
        }

        // 更新库存
        stock.setCurrentStock(newStock);
        stockMapper.updateById(stock);

        return newStock;
    }
}
