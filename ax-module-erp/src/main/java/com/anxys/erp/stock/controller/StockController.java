package com.anxys.erp.stock.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.anxys.erp.stock.domain.excel.StockExcel;
import com.anxys.erp.stock.domain.form.StockAddParam;
import com.anxys.erp.stock.domain.form.StockPageParam;
import com.anxys.erp.stock.domain.form.StockQueryParam;
import com.anxys.erp.stock.domain.form.StockUpdateParam;
import com.anxys.erp.stock.domain.vo.StockVO;
import com.anxys.erp.stock.service.StockService;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.common.utils.DateFormatterEnum;
import com.anxys.framework.common.utils.ExcelUtil;
import com.anxys.framework.common.utils.LocalDateUtil;
import com.anxys.system.utils.AdminRequestUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存信息控制器
 *
 * @since 2025-06-09 16:58:32
 */
@Slf4j
@RestController
@RequestMapping("/stock")
@Tag(name = "库存信息接口")
public class StockController {

    @Resource
    private StockService stockService;

    @Operation(summary = "库存信息分页查询")
    @PostMapping("/stockPage")
    @SaCheckPermission("stock:query")
    public HttpResponse<PageResult<StockVO>> query(@RequestBody @Valid StockPageParam queryParam) {
        PageResult<StockVO> pageResult = stockService.page(queryParam);
        return HttpResponse.ok(pageResult);
    }

    @Operation(summary = "库存信息列表查询")
    @PostMapping("/stockList")
    @SaCheckPermission("stock:query")
    public HttpResponse<List<StockVO>> list(@RequestBody @Valid StockQueryParam queryParam) {
        List<StockVO> list = stockService.list(queryParam);
        return HttpResponse.ok(list);
    }

    @Operation(summary = "获取库存信息详情")
    @GetMapping("/stockDetail/{id}")
    @SaCheckPermission("stock:query")
    public HttpResponse<StockVO> detail(@PathVariable Long id) {
        StockVO stockVO = stockService.queryById(id);
        return HttpResponse.ok(stockVO);
    }

    @Operation(summary = "添加库存信息")
    @PostMapping("/addStock")
    @SaCheckPermission("stock:add")
    public HttpResponse<StockVO> insert(@RequestBody @Valid StockAddParam addParam) {
        StockVO result = stockService.insert(addParam);
        return HttpResponse.ok(result);
    }

    @Operation(summary = "更新库存信息")
    @PostMapping("/updateStock")
    @SaCheckPermission("stock:update")
    public HttpResponse<String> update(@RequestBody @Valid StockUpdateParam updateParam) {
        stockService.update(updateParam);
        return HttpResponse.ok();
    }

    @Operation(summary = "删除库存信息")
    @GetMapping("/deleteStock/{id}")
    @SaCheckPermission("stock:delete")
    public HttpResponse<String> delete(@PathVariable Long id) {
        stockService.delete(id);
        return HttpResponse.ok();
    }

    @Operation(summary = "批量删除库存信息")
    @PostMapping("/batchDeleteStock")
    @SaCheckPermission("stock:delete")
    public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
        stockService.batchDelete(idList);
        return HttpResponse.ok();
    }

    @Operation(summary = "导出库存信息Excel")
    @GetMapping("/exportStockExcel")
    @SaCheckPermission("stock:export")
    public void exportExcel(@Parameter(description = "查询参数") StockQueryParam queryParam, HttpServletResponse response) throws IOException {
        List<StockExcel> list = stockService.exportExcel(queryParam);
        // 添加水印
        String watermark = AdminRequestUtil.getRequestUser().getActualName();
        watermark += LocalDateUtil.format(LocalDateTime.now(), DateFormatterEnum.YMD_HMS);

        ExcelUtil.exportExcelWithWatermark(response, "库存信息列表.xlsx", "库存信息", StockExcel.class, list, watermark);
    }

    @Operation(summary = "导入库存信息Excel")
    @PostMapping("/importStockExcel")
    @SaCheckPermission("stock:import")
    public HttpResponse<String> importExcel(@RequestParam("file") MultipartFile file) {
        int count = stockService.importExcel(file);
        return HttpResponse.okMsg("成功导入" + count + "条数据");
    }
}
