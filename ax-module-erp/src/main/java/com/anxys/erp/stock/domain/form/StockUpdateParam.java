package com.anxys.erp.stock.domain.form;


import com.anxys.erp.stock.domain.Stock;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存信息更新表单
 * @since 2025-06-09 16:58:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockUpdateParam extends Stock {

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;

}
