package com.anxys.erp.stock.domain.form;


import com.anxys.erp.stock.domain.Stock;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 库存信息查询表单
 *
 * @since 2025-06-09 16:58:32
 */
@EqualsAndHashCode(callSuper = false)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class StockQueryParam extends Stock {

            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "最后入库日期始")
            private LocalDate lastInDateFrom;

            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "最后入库日期至")
            private LocalDate lastInDateTo;
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "最后出库日期始")
            private LocalDate lastOutDateFrom;

            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "最后出库日期至")
            private LocalDate lastOutDateTo;
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "最后盘点日期始")
            private LocalDate lastCheckDateFrom;

            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "最后盘点日期至")
            private LocalDate lastCheckDateTo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "创建时间始")
            private LocalDateTime createTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "创建时间至")
            private LocalDateTime createTimeTo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "更新时间始")
            private LocalDateTime updateTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "更新时间至")
            private LocalDateTime updateTimeTo;
}
