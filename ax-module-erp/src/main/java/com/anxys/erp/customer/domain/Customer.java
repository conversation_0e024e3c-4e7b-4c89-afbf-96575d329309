package com.anxys.erp.customer.domain;

import com.anxys.framework.common.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;


/**
 * 客户信息实体类
 *
 * @since 2025-06-11 21:41:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("erp_customer")
public class Customer extends BaseEntity {
    @Schema(description = "主键id")
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
    @Schema(description = "客户编码")
    private String customerCode;
    @Schema(description = "客户名称")
    private String customerName;
    @Schema(description = "字典 | 客户类型 customer_type：REGULAR-普通客户，VIP-VIP客户，STRATEGIC-战略客户，DISTRIBUTOR-经销商，AGENT-代理商")
    private String customerType;
    @Schema(description = "字典 | 信用等级 credit_level：AAA-AAA级，AA-AA级，A-A级，BBB-BBB级，BB-BB级，B-B级，CCC-CCC级，CC-CC级，C-C级")
    private String creditLevel;
    @Schema(description = "联系人姓名")
    private String contactName;
    @Schema(description = "")
    private String contactPosition;
    @Schema(description = "")
    private String contactDepartment;
    @Schema(description = "联系人电话")
    private String contactPhone;
    @Schema(description = "联系人手机")
    private String contactMobile;
    @Schema(description = "联系人邮箱")
    private String contactEmail;
    @Schema(description = "营业执照号")
    private String businessLicense;
    @Schema(description = "税务登记号")
    private String taxId;
    @Schema(description = "地址信息列表(jsonb数组)")
    private List<Map<String, Object>> addressInfoList;
    @Schema(description = "支付账户信息列表(jsonb数组)")
    private List<Map<String, Object>> paymentInfoList;
    @Schema(description = "字典 | 客户状态 customer_status：ACTIVE-活跃，INACTIVE-非活跃，BLACKLIST-黑名单，POTENTIAL-潜在客户")
    private String status;
    @Schema(description = "备注")
    private String remark;
}
