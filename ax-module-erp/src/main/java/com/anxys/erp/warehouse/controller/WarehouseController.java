package com.anxys.erp.warehouse.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.anxys.erp.warehouse.domain.excel.WarehouseExcel;
import com.anxys.erp.warehouse.domain.form.WarehouseAddParam;
import com.anxys.erp.warehouse.domain.form.WarehousePageParam;
import com.anxys.erp.warehouse.domain.form.WarehouseQueryParam;
import com.anxys.erp.warehouse.domain.form.WarehouseUpdateParam;
import com.anxys.erp.warehouse.domain.vo.WarehouseVO;
import com.anxys.erp.warehouse.service.WarehouseService;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.common.utils.DateFormatterEnum;
import com.anxys.framework.common.utils.ExcelUtil;
import com.anxys.framework.common.utils.LocalDateUtil;
import com.anxys.system.utils.AdminRequestUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 仓库信息控制器
 *
 * @since 2025-05-28 11:33:57
 */
@Slf4j
@RestController
@RequestMapping("/warehouse")
@Tag(name = "仓库信息接口")
public class WarehouseController {

    @Resource
    private WarehouseService warehouseService;

    @Operation(summary = "仓库信息分页查询")
    @PostMapping("/warehousePage")
    @SaCheckPermission("warehouse:query")
    public HttpResponse<PageResult<WarehouseVO>> query(@RequestBody @Valid WarehousePageParam queryParam) {
        PageResult<WarehouseVO> pageResult = warehouseService.page(queryParam);
        return HttpResponse.ok(pageResult);
    }

    @Operation(summary = "仓库信息列表查询")
    @PostMapping("/warehouseList")
    @SaCheckPermission("warehouse:query")
    public HttpResponse<List<WarehouseVO>> list(@RequestBody @Valid WarehouseQueryParam queryParam) {
        List<WarehouseVO> list = warehouseService.list(queryParam);
        return HttpResponse.ok(list);
    }

    @Operation(summary = "获取仓库信息详情")
    @GetMapping("/warehouseDetail/{id}")
    @SaCheckPermission("warehouse:query")
    public HttpResponse<WarehouseVO> detail(@PathVariable Long id) {
        WarehouseVO warehouseVO = warehouseService.queryById(id);
        return HttpResponse.ok(warehouseVO);
    }

    @Operation(summary = "添加仓库信息")
    @PostMapping("/addWarehouse")
    @SaCheckPermission("warehouse:add")
    public HttpResponse<WarehouseVO> insert(@RequestBody @Valid WarehouseAddParam addParam) {
        WarehouseVO result = warehouseService.insert(addParam);
        return HttpResponse.ok(result);
    }

    @Operation(summary = "更新仓库信息")
    @PostMapping("/updateWarehouse")
    @SaCheckPermission("warehouse:update")
    public HttpResponse<String> update(@RequestBody @Valid WarehouseUpdateParam updateParam) {
        warehouseService.update(updateParam);
        return HttpResponse.ok();
    }

    @Operation(summary = "删除仓库信息")
    @GetMapping("/deleteWarehouse/{id}")
    @SaCheckPermission("warehouse:delete")
    public HttpResponse<String> delete(@PathVariable Long id) {
        warehouseService.delete(id);
        return HttpResponse.ok();
    }

    @Operation(summary = "批量删除仓库信息")
    @PostMapping("/batchDeleteWarehouse")
    @SaCheckPermission("warehouse:delete")
    public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
        warehouseService.batchDelete(idList);
        return HttpResponse.ok();
    }

    @Operation(summary = "导出仓库信息Excel")
    @GetMapping("/exportWarehouseExcel")
    @SaCheckPermission("warehouse:export")
    public void exportExcel(@Parameter(description = "查询参数") WarehouseQueryParam queryParam, HttpServletResponse response) throws IOException {
        List<WarehouseExcel> list = warehouseService.exportExcel(queryParam);
        // 添加水印
        String watermark = AdminRequestUtil.getRequestUser().getActualName();
        watermark += LocalDateUtil.format(LocalDateTime.now(), DateFormatterEnum.YMD_HMS);

        ExcelUtil.exportExcelWithWatermark(response, "仓库信息列表.xlsx", "仓库信息", WarehouseExcel.class, list, watermark);
    }

    @Operation(summary = "导入仓库信息Excel")
    @PostMapping("/importWarehouseExcel")
    @SaCheckPermission("warehouse:import")
    public HttpResponse<String> importExcel(@RequestParam("file") MultipartFile file) {
        int count = warehouseService.importExcel(file);
        return HttpResponse.okMsg("成功导入" + count + "条数据");
    }

    /**
     * 获取所有仓库列表（GET方式，用于下拉选择）
     */
    @Operation(summary = "获取所有仓库列表")
    @GetMapping("/warehouseList")
    @SaCheckPermission("warehouse:query")
    public HttpResponse<List<WarehouseVO>> warehouseList() {
        List<WarehouseVO> list = warehouseService.getAllWarehouses();
        return HttpResponse.ok(list);
    }

    /**
     * 获取活跃仓库列表（GET方式，用于下拉选择）
     */
    @Operation(summary = "获取活跃仓库列表")
    @GetMapping("/activeWarehouseList")
    @SaCheckPermission("warehouse:query")
    public HttpResponse<List<WarehouseVO>> activeWarehouseList() {
        List<WarehouseVO> list = warehouseService.getActiveWarehouses();
        return HttpResponse.ok(list);
    }
}
