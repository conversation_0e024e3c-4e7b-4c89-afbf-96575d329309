package com.anxys.erp.warehouse.domain.form;

import com.anxys.erp.warehouse.domain.Warehouse;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 仓库信息添加表单
 *
 * @since 2025-05-29 15:28:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WarehouseAddParam extends Warehouse {

            @NotBlank(message = "仓库编码不能为空")
        private String warehouseCode;
            @NotBlank(message = "仓库名称不能为空")
        private String warehouseName;
            @NotBlank(message = "字典 | 仓库类型 warehouse_type：RAW_MATERIAL-原材料仓库，FINISHED_GOODS-成品仓库，SEMI_FINISHED-半成品仓库，SPARE_PARTS-备件仓库，RETURN_GOODS-退货仓库，TRANSIT-中转仓库不能为空")
        private String warehouseType;
            @NotBlank(message = "字典 | 仓库状态 warehouse_status：ACTIVE-活跃，INACTIVE-非活跃，MAINTENANCE-维护中，CLOSED-已关闭不能为空")
        private String status;
}
