package com.anxys.erp.warehouse.service;

import cn.hutool.core.collection.CollUtil;
import cn.idev.excel.FastExcel;
import com.anxys.erp.warehouse.dao.WarehouseMapper;
import com.anxys.erp.warehouse.domain.Warehouse;
import com.anxys.erp.warehouse.domain.excel.WarehouseExcel;
import com.anxys.erp.warehouse.domain.form.WarehouseAddParam;
import com.anxys.erp.warehouse.domain.form.WarehousePageParam;
import com.anxys.erp.warehouse.domain.form.WarehouseQueryParam;
import com.anxys.erp.warehouse.domain.form.WarehouseUpdateParam;
import com.anxys.erp.warehouse.domain.vo.WarehouseVO;
import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import com.anxys.framework.enums.CommonStatusEnum;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.anxys.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.anxys.framework.common.utils.collection.CollectionUtils.convertMap;

/**
 * 仓库信息服务层
 *
 * @since 2025-05-28 11:33:57
 */
@Slf4j
@Service
public class WarehouseService {

    @Resource
    private WarehouseMapper warehouseMapper;

    /**
     * 分页查询
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    public PageResult<WarehouseVO> page(WarehousePageParam queryParam) {
        Page<?> page = PageUtil.convert2PageQuery(queryParam.getPageParam());
        List<WarehouseVO> PageVo = warehouseMapper.page(page, queryParam);
        return PageUtil.convert2PageResult(page, PageVo);
    }

    /**
      * 列表查询
      *
      * @param queryParam 查询参数
      * @return 列表结果
      */
    public List<WarehouseVO> list(WarehouseQueryParam queryParam) {
        return warehouseMapper.list(queryParam);
    }

    /**
     * 添加
     *
     * @param addParam 添加参数
     * @return 新创建的对象VO
     */
    @Transactional(rollbackFor = Exception.class)
    public WarehouseVO insert(WarehouseAddParam addParam) {
        Warehouse warehouse = BeanUtil.copy(addParam, Warehouse.class);
        warehouseMapper.insert(warehouse);
        // 返回新创建的对象
        return BeanUtil.copy(warehouse, WarehouseVO.class);
    }

    /**
     * 更新
     *
     * @param updateParam 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(WarehouseUpdateParam updateParam) {
        // 校验存在
        validateWarehouseExists(updateParam.getId());
        Warehouse warehouse = BeanUtil.copy(updateParam, Warehouse.class);
        warehouseMapper.updateById(warehouse);
    }

    private void validateWarehouseExists( Long id) {
        if (warehouseMapper.selectById(id) == null) {
            throw exception("仓库不存在");
        }
    }
    public List<Warehouse> validWarehouseList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<Warehouse> list = warehouseMapper.selectByIds(ids);
        Map<Long, Warehouse> warehouseMap = convertMap(list, Warehouse::getId);
        for (Long id : ids) {
            Warehouse warehouse = warehouseMap.get(id);
            if (warehouseMap.get(id) == null) {
                throw exception("仓库不存在");
            }
            if (CommonStatusEnum.isDisable(warehouse.getStatus())) {
                throw exception("仓库未启用");
            }
        }
        return list;
    }



    /**
     * 批量删除
     *
     * @param idList ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        if (!idList.isEmpty()) {
            warehouseMapper.deleteByIds(idList, true);
        }
    }

    /**
     * 单个删除
     *
     * @param id ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        warehouseMapper.deleteById(id);
    }

    /**
     * 通过ID查询
     *
     * @param id ID
     * @return 详情
     */
    public WarehouseVO queryById(Long id) {
        Warehouse warehouse = warehouseMapper.selectById(id);
        if (warehouse == null) {
            throw new BusinessException(UserErrorCode.DATA_NOT_EXIST);
        }
        return BeanUtil.copy(warehouse, WarehouseVO.class);
    }

    /**
     * 导出Excel
     *
     * @param queryParam 查询参数
     * @return Excel数据列表
     */
    public List<WarehouseExcel> exportExcel(WarehouseQueryParam queryParam) {
        return warehouseMapper.exportExcel(queryParam);
    }


    /**
     * 导入Excel
     *
     * @param file Excel文件
     * @return 导入的数据条数
     */
    @Transactional(rollbackFor = Exception.class)
    public int importExcel(MultipartFile file) {
        List<WarehouseExcel> dataList;
        try {
            dataList = FastExcel.read(file.getInputStream())
                    .head(WarehouseExcel.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            log.error("Excel导入失败", e);
            throw new BusinessException("数据格式存在问题，无法读取");
        }

        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException("数据为空");
        }

        // 处理导入数据
        log.info("开始处理导入数据，共 {} 条", dataList.size());
        List<Warehouse> entityList = dataList.stream()
                .map(excel -> {
                    log.info("处理数据: {}", excel);
                    return BeanUtil.copy(excel, Warehouse.class);
                })
                .toList();

        warehouseMapper.batchInsert(entityList);

        return dataList.size();


    }
    /**
     * 获取所有仓库列表（用于下拉选择）
     *
     * @return 仓库列表
     */
    public List<WarehouseVO> getAllWarehouses() {
        WarehouseQueryParam queryParam = new WarehouseQueryParam();
        queryParam.setDeletedFlag(false);  // 只查询未删除的仓库
        return warehouseMapper.list(queryParam);
    }

    /**
     * 获取活跃仓库列表（用于下拉选择）
     *
     * @return 活跃仓库列表
     */
    public List<WarehouseVO> getActiveWarehouses() {
        WarehouseQueryParam queryParam = new WarehouseQueryParam();
        queryParam.setDeletedFlag(false);  // 只查询未删除的仓库
        queryParam.setStatus("ACTIVE");    // 只查询活跃状态的仓库
        return warehouseMapper.list(queryParam);
    }

    /**
     * 获取默认仓库
     *
     * @return 默认仓库，如果没有则返回第一个活跃仓库
     */
    public WarehouseVO getDefaultWarehouse() {
        // 首先查询设置为默认的仓库
        Warehouse defaultWarehouse = warehouseMapper.selectDefaultWarehouse();

        if (defaultWarehouse != null) {
            return BeanUtil.copy(defaultWarehouse, WarehouseVO.class);
        }

        // 如果没有设置默认仓库，则返回第一个活跃仓库
        List<WarehouseVO> activeWarehouses = getActiveWarehouses();
        if (!activeWarehouses.isEmpty()) {
            return activeWarehouses.get(0);
        }

        // 如果没有活跃仓库，抛出异常
        throw new BusinessException("系统中没有可用的仓库，请先创建并设置默认仓库");
    }

}
