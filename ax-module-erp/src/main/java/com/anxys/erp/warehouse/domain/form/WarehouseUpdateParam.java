package com.anxys.erp.warehouse.domain.form;


import com.anxys.erp.warehouse.domain.Warehouse;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仓库信息更新表单
 * @since 2025-05-29 15:28:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WarehouseUpdateParam extends Warehouse {

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;

}
