package com.anxys.erp.warehouse.domain.form;

import com.anxys.framework.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 仓库信息分页查询表单
 *
 * @since 2025-05-29 15:28:57
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class WarehousePageParam extends WarehouseQueryParam {

    @Schema(description = "分页参数")
    @Valid
    @NotNull(message = "分页参数不能为空")
    PageParam pageParam = new PageParam();

}
