package com.anxys.erp.warehouse.domain;

import com.anxys.framework.common.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * 仓库信息实体类
 *
 * @since 2025-05-29 15:28:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("erp_warehouse")
public class Warehouse extends BaseEntity {
    @Schema(description = "主键id")
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
    @Schema(description = "仓库编码")
    private String warehouseCode;
    @Schema(description = "仓库名称")
    private String warehouseName;
    @Schema(description = "字典 | 仓库类型 warehouse_type：RAW_MATERIAL-原材料仓库，FINISHED_GOODS-成品仓库，SEMI_FINISHED-半成品仓库，SPARE_PARTS-备件仓库，RETURN_GOODS-退货仓库，TRANSIT-中转仓库")
    private String warehouseType;
    @Schema(description = "省份")
    private String province;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "区县")
    private String district;
    @Schema(description = "详细地址")
    private String address;
    @Schema(description = "邮政编码")
    private String postalCode;
    @Schema(description = "联系人")
    private String contactPerson;
    @Schema(description = "联系电话")
    private String contactPhone;
    @Schema(description = "联系手机")
    private String contactMobile;
    @Schema(description = "联系邮箱")
    private String contactEmail;
    @Schema(description = "仓库面积(平方米)")
    private Double area;
    @Schema(description = "仓库容量")
    private Double capacity;
    @Schema(description = "是否温控：FALSE-否，TRUE-是")
    private Boolean temperatureControlled;
    @Schema(description = "最低温度")
    private Double minTemperature;
    @Schema(description = "最高温度")
    private Double maxTemperature;
    @Schema(description = "是否湿度控制：FALSE-否，TRUE-是")
    private Boolean humidityControlled;
    @Schema(description = "最低湿度")
    private Double minHumidity;
    @Schema(description = "最高湿度")
    private Double maxHumidity;
    @Schema(description = "仓库管理员ID | t_employee表的employee_id")
    private Long managerId;
    @Schema(description = "仓库管理员姓名")
    private String managerName;
    @Schema(description = "上级仓库ID | erp_warehouse表的id")
    private Long parentWarehouseId;
    @Schema(description = "仓库层级")
    private Integer warehouseLevel;
    @Schema(description = "字典 | 仓库状态 warehouse_status：ACTIVE-活跃，INACTIVE-非活跃，MAINTENANCE-维护中，CLOSED-已关闭")
    private String status;
    @Schema(description = "是否默认仓库：FALSE-否，TRUE-是")
    private Boolean isDefault;
    @Schema(description = "排序")
    private Integer sortOrder;
    @Schema(description = "备注")
    private String remark;
}
