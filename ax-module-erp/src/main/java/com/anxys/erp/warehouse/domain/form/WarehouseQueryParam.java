package com.anxys.erp.warehouse.domain.form;

import com.anxys.erp.warehouse.domain.Warehouse;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 仓库信息查询表单
 *
 * @since 2025-05-29 15:28:57
 */
@EqualsAndHashCode(callSuper = false)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseQueryParam extends Warehouse {

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "创建时间始")
            private LocalDateTime createTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "创建时间至")
            private LocalDateTime createTimeTo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "更新时间始")
            private LocalDateTime updateTimeFrom;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "更新时间至")
            private LocalDateTime updateTimeTo;
}
