package com.anxys.erp.warehouse.dao;


import com.anxys.erp.warehouse.domain.Warehouse;
import com.anxys.erp.warehouse.domain.excel.WarehouseExcel;
import com.anxys.erp.warehouse.domain.form.WarehousePageParam;
import com.anxys.erp.warehouse.domain.form.WarehouseQueryParam;
import com.anxys.erp.warehouse.domain.vo.WarehouseVO;
import com.anxys.framework.mybatis.mapper.BaseMapperX;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库信息数据库访问层
 * @since 2025-05-28 11:33:57
 */
@Mapper
public interface WarehouseMapper extends BaseMapperX<Warehouse> {

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param queryParam 查询参数
     * @return 对象列表
     */
    List<WarehouseVO> page(Page page, @Param("param") WarehousePageParam queryParam);

  /**
  * 查询列表
  * @param queryParam 查询参数
  * @return 对象列表
  *
  */
    List<WarehouseVO> list(@Param("param") WarehouseQueryParam queryParam);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList") List<Long> idList, @Param("deletedFlag") Boolean deletedFlag);


    /**
     * 导出Excel
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WarehouseExcel> exportExcel(@Param("query") WarehouseQueryParam queryParam);

    /**
     * 查询默认仓库
     *
     * @return 默认仓库
     */
    Warehouse selectDefaultWarehouse();

        void batchInsert(@Param("entity") List<Warehouse> entityList);
}
