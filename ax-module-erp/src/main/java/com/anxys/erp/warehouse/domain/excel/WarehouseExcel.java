package com.anxys.erp.warehouse.domain.excel;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 仓库信息Excel导出VO
 * @since 2025-05-29 15:28:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

@ExcelProperty("仓库编码")        private String warehouseCode;
@ExcelProperty("仓库名称")        private String warehouseName;
@ExcelProperty("字典 | 仓库类型 warehouse_type：RAW_MATERIAL-原材料仓库，FINISHED_GOODS-成品仓库，SEMI_FINISHED-半成品仓库，SPARE_PARTS-备件仓库，RETURN_GOODS-退货仓库，TRANSIT-中转仓库")        private String warehouseType;
@ExcelProperty("省份")        private String province;
@ExcelProperty("城市")        private String city;
@ExcelProperty("区县")        private String district;
@ExcelProperty("详细地址")        private String address;
@ExcelProperty("邮政编码")        private String postalCode;
@ExcelProperty("联系人")        private String contactPerson;
@ExcelProperty("联系电话")        private String contactPhone;
@ExcelProperty("联系手机")        private String contactMobile;
@ExcelProperty("联系邮箱")        private String contactEmail;
@ExcelProperty("仓库面积(平方米)")        private Double area;
@ExcelProperty("仓库容量")        private Double capacity;
@ExcelProperty("是否温控：FALSE-否，TRUE-是")        private Boolean temperatureControlled;
@ExcelProperty("最低温度")        private Double minTemperature;
@ExcelProperty("最高温度")        private Double maxTemperature;
@ExcelProperty("是否湿度控制：FALSE-否，TRUE-是")        private Boolean humidityControlled;
@ExcelProperty("最低湿度")        private Double minHumidity;
@ExcelProperty("最高湿度")        private Double maxHumidity;
@ExcelProperty("仓库管理员ID | t_employee表的employee_id")        private Long managerId;
@ExcelProperty("仓库管理员姓名")        private String managerName;
@ExcelProperty("上级仓库ID | erp_warehouse表的id")        private Long parentWarehouseId;
@ExcelProperty("仓库层级")        private Integer warehouseLevel;
@ExcelProperty("字典 | 仓库状态 warehouse_status：ACTIVE-活跃，INACTIVE-非活跃，MAINTENANCE-维护中，CLOSED-已关闭")        private String status;
@ExcelProperty("是否默认仓库：FALSE-否，TRUE-是")        private Boolean isDefault;
@ExcelProperty("排序")        private Integer sortOrder;
@ExcelProperty("备注")        private String remark;
@ExcelProperty("创建人")        private Long createdBy;
@ExcelProperty("更新人")        private Long updatedBy;
@ExcelProperty("创建时间")        private LocalDateTime createTime;
@ExcelProperty("更新时间")        private LocalDateTime updateTime;
}
