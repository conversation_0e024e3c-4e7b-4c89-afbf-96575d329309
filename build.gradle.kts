// ========================================
// ax-admin 根项目构建配置 🚀
// 统一依赖管理和构建配置
// ========================================

import org.gradle.api.artifacts.VersionCatalogsExtension

plugins {
    // 应用于所有子项目的插件
    id("org.springframework.boot") version "3.4.5" apply false
    id("io.spring.dependency-management") version "1.1.6" apply false
    id("com.diffplug.spotless") version "6.25.0" apply false
}

// 统一配置所有子项目
subprojects {
    // 应用通用配置
    group = "com.anxys"
    version = "3.0.0"

    // 应用通用插件
    apply(plugin = "java")
    apply(plugin = "com.diffplug.spotless")

    // 统一排除 spring-boot-starter-logging，避免日志冲突
    configurations.all {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-logging")
    }

    // 通用依赖配置
    afterEvaluate {
        dependencies {
            val libs = rootProject.extensions.getByType<VersionCatalogsExtension>().named("libs")

            // 导入 BOM 平台，统一版本管理
            "implementation"(platform(libs.findLibrary("spring-boot-bom").get()))
            "implementation"(platform(libs.findLibrary("spring-framework-bom").get()))
            "implementation"(platform(libs.findLibrary("spring-ai-bom").get()))

            "compileOnly"(libs.findLibrary("lombok").get())
            "annotationProcessor"(libs.findLibrary("lombok").get())
            "testImplementation"(libs.findLibrary("spring-boot-starter-testing").get())
            "testCompileOnly"(libs.findLibrary("lombok").get())
            "testAnnotationProcessor"(libs.findLibrary("lombok").get())
            "testRuntimeOnly"(libs.findLibrary("h2").get())
        }
    }

    // Java 编译配置
    configure<JavaPluginExtension> {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        withSourcesJar()

        // 改善 IDEA 集成
        toolchain {
            languageVersion.set(JavaLanguageVersion.of(17))
        }
    }

    // 编译任务配置
    tasks.withType<JavaCompile> {
        options.isIncremental = true
        options.encoding = "UTF-8"
        options.compilerArgs.addAll(listOf(
            "-parameters",
            "-Xlint:unchecked",
            "-Xlint:deprecation"
        ))
    }

    // 测试配置
    tasks.withType<Test> {
        useJUnitPlatform()
        testLogging {
            events("passed", "skipped", "failed")
            showStandardStreams = false
        }
        // 测试并行执行
        maxParallelForks = (Runtime.getRuntime().availableProcessors() / 2).takeIf { it > 0 } ?: 1
    }

    // 代码格式化 - 只保留import限制
    configure<com.diffplug.gradle.spotless.SpotlessExtension> {
        java {
            removeUnusedImports()
            // 移除其他格式化规则，只保留import管理
        }
    }

    // IDEA 兼容性配置
    configurations.all {
        // 确保依赖解析的一致性
        resolutionStrategy {
            // 缓存动态版本1小时
            cacheDynamicVersionsFor(1, java.util.concurrent.TimeUnit.HOURS)
            // 缓存变化模块1小时
            cacheChangingModulesFor(1, java.util.concurrent.TimeUnit.HOURS)

            // 强制使用一致的版本
            force(
                "org.slf4j:slf4j-api:2.0.17",
                "com.fasterxml.jackson.core:jackson-core:2.18.3",
                "com.fasterxml.jackson.core:jackson-databind:2.18.3",
                "com.fasterxml.jackson.core:jackson-annotations:2.18.3"
            )
        }
    }
}

// 简化的根项目任务
// 使用标准Gradle任务：
// - ./gradlew projects (查看项目结构)
// - ./gradlew dependencies (查看依赖)
// - ./gradlew tasks (查看所有任务)
// - ./gradlew clean (清理所有项目)
// - ./gradlew build (构建所有项目)

