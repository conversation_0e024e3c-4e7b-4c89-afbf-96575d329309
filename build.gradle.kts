// ========================================
// ax-admin 根项目构建配置 🚀
// 统一依赖管理和构建配置
// ========================================

plugins {
    // 应用于所有子项目的插件
    id("org.springframework.boot") version "3.4.5" apply false
    id("io.spring.dependency-management") version "1.1.6" apply false
    id("com.diffplug.spotless") version "6.25.0" apply false
}

// 统一配置所有子项目
subprojects {
    // 应用通用配置
    group = "com.anxys"
    version = "3.0.0"

    // 统一排除 spring-boot-starter-logging，避免日志冲突
    configurations.all {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-logging")
    }

    // 仓库配置已在 settings.gradle.kts 中统一管理，无需重复配置
}

// 根项目任务 - 使用配置缓存兼容的方式
tasks.register("showDependencyTree") {
    description = "显示所有子项目的依赖树"
    group = "help"

    doLast {
        println("=== 依赖树信息 ===")
        println("请使用以下命令查看具体模块的依赖:")
        println("  ./gradlew :ax-framework:dependencies")
        println("  ./gradlew :ax-server:dependencies")
        println("  ./gradlew :ax-module-system:dependencies")
        println("  ./gradlew :ax-module-erp:dependencies")
        println("  ./gradlew :ax-module-ai:dependencies")
    }
}

// 统一清理任务
tasks.register("cleanAll") {
    description = "清理所有子项目的构建文件"
    group = "build"
    
    dependsOn(subprojects.map { "${it.path}:clean" })
}

// 统一构建任务
tasks.register("buildAll") {
    description = "构建所有子项目"
    group = "build"
    
    dependsOn(subprojects.map { "${it.path}:build" })
}
